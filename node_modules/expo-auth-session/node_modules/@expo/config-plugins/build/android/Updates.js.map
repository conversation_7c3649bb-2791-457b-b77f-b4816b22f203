{"version": 3, "file": "Updates.js", "names": ["_Manifest", "data", "require", "_Resources", "_Strings", "_androidPlugins", "_withPlugins", "_Updates", "_warnings", "Config", "exports", "withUpdates", "config", "with<PERSON><PERSON><PERSON>", "withUpdatesManifest", "withRuntimeVersionResource", "withAndroidManifest", "projectRoot", "modRequest", "expoUpdatesPackageVersion", "getExpoUpdatesPackageVersion", "modResults", "setUpdatesConfigAsync", "createStringsXmlPlugin", "applyRuntimeVersionFromConfigAsync", "stringsJSON", "applyRuntimeVersionFromConfigForProjectRootAsync", "runtimeVersion", "getRuntimeVersionNullableAsync", "setStringItem", "buildResourceItem", "name", "value", "removeStringItem", "androidManifest", "mainApplication", "getMainApplicationOrThrow", "addMetaDataItemToMainApplication", "ENABLED", "String", "getUpdatesEnabled", "checkOnLaunch", "getUpdatesCheckOnLaunch", "CHECK_ON_LAUNCH", "timeout", "getUpdatesTimeout", "LAUNCH_WAIT_MS", "useEmbeddedUpdate", "getUpdatesUseEmbeddedUpdate", "removeMetaDataItemFromMainApplication", "UPDATES_HAS_EMBEDDED_UPDATE", "addWarningAndroid", "updateUrl", "getUpdateUrl", "UPDATE_URL", "codeSigningCertificate", "getUpdatesCodeSigningCertificate", "CODE_SIGNING_CERTIFICATE", "codeSigningMetadata", "getUpdatesCodeSigningMetadataStringified", "CODE_SIGNING_METADATA", "requestHeaders", "getUpdatesRequestHeadersStringified", "UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY", "setVersionsConfigAsync", "findMetaDataItem", "RUNTIME_VERSION", "Error"], "sources": ["../../src/android/Updates.ts"], "sourcesContent": ["import { Resources } from '.';\nimport {\n  addMetaDataItemToMainApplication,\n  AndroidManifest,\n  findMetaDataItem,\n  getMainApplicationOrThrow,\n  removeMetaDataItemFromMainApplication,\n} from './Manifest';\nimport { buildResourceItem, ResourceXML } from './Resources';\nimport { removeStringItem, setStringItem } from './Strings';\nimport { ConfigPlugin, ExportedConfigWithProps } from '../Plugin.types';\nimport { createStringsXmlPlugin, withAndroidManifest } from '../plugins/android-plugins';\nimport { withPlugins } from '../plugins/withPlugins';\nimport {\n  ExpoConfigUpdates,\n  getExpoUpdatesPackageVersion,\n  getRuntimeVersionNullableAsync,\n  getUpdatesCheckOnLaunch,\n  getUpdatesCodeSigningCertificate,\n  getUpdatesCodeSigningMetadataStringified,\n  getUpdatesRequestHeadersStringified,\n  getUpdatesEnabled,\n  getUpdatesTimeout,\n  getUpdateUrl,\n  getUpdatesUseEmbeddedUpdate,\n} from '../utils/Updates';\nimport { addWarningAndroid } from '../utils/warnings';\n\nexport enum Config {\n  ENABLED = 'expo.modules.updates.ENABLED',\n  CHECK_ON_LAUNCH = 'expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH',\n  LAUNCH_WAIT_MS = 'expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS',\n  RUNTIME_VERSION = 'expo.modules.updates.EXPO_RUNTIME_VERSION',\n  UPDATE_URL = 'expo.modules.updates.EXPO_UPDATE_URL',\n  UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY = 'expo.modules.updates.UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY',\n  UPDATES_HAS_EMBEDDED_UPDATE = 'expo.modules.updates.HAS_EMBEDDED_UPDATE',\n  CODE_SIGNING_CERTIFICATE = 'expo.modules.updates.CODE_SIGNING_CERTIFICATE',\n  CODE_SIGNING_METADATA = 'expo.modules.updates.CODE_SIGNING_METADATA',\n}\n\n// when making changes to this config plugin, ensure the same changes are also made in eas-cli and build-tools\n// Also ensure the docs are up-to-date: https://docs.expo.dev/bare/installing-updates/\n\nexport const withUpdates: ConfigPlugin = (config) => {\n  return withPlugins(config, [withUpdatesManifest, withRuntimeVersionResource]);\n};\n\nconst withUpdatesManifest: ConfigPlugin = (config) => {\n  return withAndroidManifest(config, async (config) => {\n    const projectRoot = config.modRequest.projectRoot;\n    const expoUpdatesPackageVersion = getExpoUpdatesPackageVersion(projectRoot);\n    config.modResults = await setUpdatesConfigAsync(\n      projectRoot,\n      config,\n      config.modResults,\n      expoUpdatesPackageVersion\n    );\n    return config;\n  });\n};\n\nconst withRuntimeVersionResource = createStringsXmlPlugin(\n  applyRuntimeVersionFromConfigAsync,\n  'withRuntimeVersionResource'\n);\n\nexport async function applyRuntimeVersionFromConfigAsync(\n  config: ExportedConfigWithProps<Resources.ResourceXML>,\n  stringsJSON: ResourceXML\n): Promise<ResourceXML> {\n  const projectRoot = config.modRequest.projectRoot;\n  return await applyRuntimeVersionFromConfigForProjectRootAsync(projectRoot, config, stringsJSON);\n}\n\nexport async function applyRuntimeVersionFromConfigForProjectRootAsync(\n  projectRoot: string,\n  config: ExpoConfigUpdates,\n  stringsJSON: ResourceXML\n): Promise<ResourceXML> {\n  const runtimeVersion = await getRuntimeVersionNullableAsync(projectRoot, config, 'android');\n  if (runtimeVersion) {\n    return setStringItem(\n      [buildResourceItem({ name: 'expo_runtime_version', value: runtimeVersion })],\n      stringsJSON\n    );\n  }\n  return removeStringItem('expo_runtime_version', stringsJSON);\n}\n\nexport async function setUpdatesConfigAsync(\n  projectRoot: string,\n  config: ExpoConfigUpdates,\n  androidManifest: AndroidManifest,\n  expoUpdatesPackageVersion?: string | null\n): Promise<AndroidManifest> {\n  const mainApplication = getMainApplicationOrThrow(androidManifest);\n\n  addMetaDataItemToMainApplication(\n    mainApplication,\n    Config.ENABLED,\n    String(getUpdatesEnabled(config))\n  );\n  const checkOnLaunch = getUpdatesCheckOnLaunch(config, expoUpdatesPackageVersion);\n  addMetaDataItemToMainApplication(mainApplication, Config.CHECK_ON_LAUNCH, checkOnLaunch);\n\n  const timeout = getUpdatesTimeout(config);\n  addMetaDataItemToMainApplication(mainApplication, Config.LAUNCH_WAIT_MS, String(timeout));\n\n  const useEmbeddedUpdate = getUpdatesUseEmbeddedUpdate(config);\n  if (useEmbeddedUpdate) {\n    removeMetaDataItemFromMainApplication(mainApplication, Config.UPDATES_HAS_EMBEDDED_UPDATE);\n  } else {\n    // TODO: is there a better place for this validation?\n    if (timeout === 0 && checkOnLaunch !== 'ALWAYS') {\n      addWarningAndroid(\n        'updates.useEmbeddedUpdate',\n        `updates.checkOnLaunch should be set to \"ON_LOAD\" and updates.fallbackToCacheTimeout should be set to a non-zero value when updates.useEmbeddedUpdate is set to false. This is because an update must be fetched on the initial launch, when no embedded update is available.`\n      );\n    }\n    addMetaDataItemToMainApplication(mainApplication, Config.UPDATES_HAS_EMBEDDED_UPDATE, 'false');\n  }\n\n  const updateUrl = getUpdateUrl(config);\n  if (updateUrl) {\n    addMetaDataItemToMainApplication(mainApplication, Config.UPDATE_URL, updateUrl);\n  } else {\n    removeMetaDataItemFromMainApplication(mainApplication, Config.UPDATE_URL);\n  }\n\n  const codeSigningCertificate = getUpdatesCodeSigningCertificate(projectRoot, config);\n  if (codeSigningCertificate) {\n    addMetaDataItemToMainApplication(\n      mainApplication,\n      Config.CODE_SIGNING_CERTIFICATE,\n      codeSigningCertificate\n    );\n  } else {\n    removeMetaDataItemFromMainApplication(mainApplication, Config.CODE_SIGNING_CERTIFICATE);\n  }\n\n  const codeSigningMetadata = getUpdatesCodeSigningMetadataStringified(config);\n  if (codeSigningMetadata) {\n    addMetaDataItemToMainApplication(\n      mainApplication,\n      Config.CODE_SIGNING_METADATA,\n      codeSigningMetadata\n    );\n  } else {\n    removeMetaDataItemFromMainApplication(mainApplication, Config.CODE_SIGNING_METADATA);\n  }\n\n  const requestHeaders = getUpdatesRequestHeadersStringified(config);\n  if (requestHeaders) {\n    addMetaDataItemToMainApplication(\n      mainApplication,\n      Config.UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY,\n      requestHeaders\n    );\n  } else {\n    removeMetaDataItemFromMainApplication(\n      mainApplication,\n      Config.UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY\n    );\n  }\n\n  return await setVersionsConfigAsync(projectRoot, config, androidManifest);\n}\n\nexport async function setVersionsConfigAsync(\n  projectRoot: string,\n  config: Pick<ExpoConfigUpdates, 'sdkVersion' | 'runtimeVersion'>,\n  androidManifest: AndroidManifest\n): Promise<AndroidManifest> {\n  const mainApplication = getMainApplicationOrThrow(androidManifest);\n\n  const runtimeVersion = await getRuntimeVersionNullableAsync(projectRoot, config, 'android');\n  if (!runtimeVersion && findMetaDataItem(mainApplication, Config.RUNTIME_VERSION) > -1) {\n    throw new Error(\n      'A runtime version is set in your AndroidManifest.xml, but is missing from your app.json/app.config.js. Please either set runtimeVersion in your app.json/app.config.js or remove expo.modules.updates.EXPO_RUNTIME_VERSION from your AndroidManifest.xml.'\n    );\n  }\n  if (runtimeVersion) {\n    removeMetaDataItemFromMainApplication(mainApplication, 'expo.modules.updates.EXPO_SDK_VERSION');\n    addMetaDataItemToMainApplication(\n      mainApplication,\n      Config.RUNTIME_VERSION,\n      '@string/expo_runtime_version'\n    );\n  } else {\n    removeMetaDataItemFromMainApplication(mainApplication, Config.RUNTIME_VERSION);\n    removeMetaDataItemFromMainApplication(mainApplication, 'expo.modules.updates.EXPO_SDK_VERSION');\n  }\n\n  return androidManifest;\n}\n"], "mappings": ";;;;;;;;;;;AACA,SAAAA,UAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,SAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAOA,SAAAE,WAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,UAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,SAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,QAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAI,gBAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,eAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,aAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,YAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,SAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,QAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAaA,SAAAO,UAAA;EAAA,MAAAP,IAAA,GAAAC,OAAA;EAAAM,SAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAsD,IAE1CQ,MAAM,GAAAC,OAAA,CAAAD,MAAA,0BAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAAA,OAANA,MAAM;AAAA,OAYlB;AACA;AAEO,MAAME,WAAyB,GAAIC,MAAM,IAAK;EACnD,OAAO,IAAAC,0BAAW,EAACD,MAAM,EAAE,CAACE,mBAAmB,EAAEC,0BAA0B,CAAC,CAAC;AAC/E,CAAC;AAACL,OAAA,CAAAC,WAAA,GAAAA,WAAA;AAEF,MAAMG,mBAAiC,GAAIF,MAAM,IAAK;EACpD,OAAO,IAAAI,qCAAmB,EAACJ,MAAM,EAAE,MAAOA,MAAM,IAAK;IACnD,MAAMK,WAAW,GAAGL,MAAM,CAACM,UAAU,CAACD,WAAW;IACjD,MAAME,yBAAyB,GAAG,IAAAC,uCAA4B,EAACH,WAAW,CAAC;IAC3EL,MAAM,CAACS,UAAU,GAAG,MAAMC,qBAAqB,CAC7CL,WAAW,EACXL,MAAM,EACNA,MAAM,CAACS,UAAU,EACjBF,yBACF,CAAC;IACD,OAAOP,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAED,MAAMG,0BAA0B,GAAG,IAAAQ,wCAAsB,EACvDC,kCAAkC,EAClC,4BACF,CAAC;AAEM,eAAeA,kCAAkCA,CACtDZ,MAAsD,EACtDa,WAAwB,EACF;EACtB,MAAMR,WAAW,GAAGL,MAAM,CAACM,UAAU,CAACD,WAAW;EACjD,OAAO,MAAMS,gDAAgD,CAACT,WAAW,EAAEL,MAAM,EAAEa,WAAW,CAAC;AACjG;AAEO,eAAeC,gDAAgDA,CACpET,WAAmB,EACnBL,MAAyB,EACzBa,WAAwB,EACF;EACtB,MAAME,cAAc,GAAG,MAAM,IAAAC,yCAA8B,EAACX,WAAW,EAAEL,MAAM,EAAE,SAAS,CAAC;EAC3F,IAAIe,cAAc,EAAE;IAClB,OAAO,IAAAE,wBAAa,EAClB,CAAC,IAAAC,8BAAiB,EAAC;MAAEC,IAAI,EAAE,sBAAsB;MAAEC,KAAK,EAAEL;IAAe,CAAC,CAAC,CAAC,EAC5EF,WACF,CAAC;EACH;EACA,OAAO,IAAAQ,2BAAgB,EAAC,sBAAsB,EAAER,WAAW,CAAC;AAC9D;AAEO,eAAeH,qBAAqBA,CACzCL,WAAmB,EACnBL,MAAyB,EACzBsB,eAAgC,EAChCf,yBAAyC,EACf;EAC1B,MAAMgB,eAAe,GAAG,IAAAC,qCAAyB,EAACF,eAAe,CAAC;EAElE,IAAAG,4CAAgC,EAC9BF,eAAe,EACf1B,MAAM,CAAC6B,OAAO,EACdC,MAAM,CAAC,IAAAC,4BAAiB,EAAC5B,MAAM,CAAC,CAClC,CAAC;EACD,MAAM6B,aAAa,GAAG,IAAAC,kCAAuB,EAAC9B,MAAM,EAAEO,yBAAyB,CAAC;EAChF,IAAAkB,4CAAgC,EAACF,eAAe,EAAE1B,MAAM,CAACkC,eAAe,EAAEF,aAAa,CAAC;EAExF,MAAMG,OAAO,GAAG,IAAAC,4BAAiB,EAACjC,MAAM,CAAC;EACzC,IAAAyB,4CAAgC,EAACF,eAAe,EAAE1B,MAAM,CAACqC,cAAc,EAAEP,MAAM,CAACK,OAAO,CAAC,CAAC;EAEzF,MAAMG,iBAAiB,GAAG,IAAAC,sCAA2B,EAACpC,MAAM,CAAC;EAC7D,IAAImC,iBAAiB,EAAE;IACrB,IAAAE,iDAAqC,EAACd,eAAe,EAAE1B,MAAM,CAACyC,2BAA2B,CAAC;EAC5F,CAAC,MAAM;IACL;IACA,IAAIN,OAAO,KAAK,CAAC,IAAIH,aAAa,KAAK,QAAQ,EAAE;MAC/C,IAAAU,6BAAiB,EACf,2BAA2B,EAC3B,8QACF,CAAC;IACH;IACA,IAAAd,4CAAgC,EAACF,eAAe,EAAE1B,MAAM,CAACyC,2BAA2B,EAAE,OAAO,CAAC;EAChG;EAEA,MAAME,SAAS,GAAG,IAAAC,uBAAY,EAACzC,MAAM,CAAC;EACtC,IAAIwC,SAAS,EAAE;IACb,IAAAf,4CAAgC,EAACF,eAAe,EAAE1B,MAAM,CAAC6C,UAAU,EAAEF,SAAS,CAAC;EACjF,CAAC,MAAM;IACL,IAAAH,iDAAqC,EAACd,eAAe,EAAE1B,MAAM,CAAC6C,UAAU,CAAC;EAC3E;EAEA,MAAMC,sBAAsB,GAAG,IAAAC,2CAAgC,EAACvC,WAAW,EAAEL,MAAM,CAAC;EACpF,IAAI2C,sBAAsB,EAAE;IAC1B,IAAAlB,4CAAgC,EAC9BF,eAAe,EACf1B,MAAM,CAACgD,wBAAwB,EAC/BF,sBACF,CAAC;EACH,CAAC,MAAM;IACL,IAAAN,iDAAqC,EAACd,eAAe,EAAE1B,MAAM,CAACgD,wBAAwB,CAAC;EACzF;EAEA,MAAMC,mBAAmB,GAAG,IAAAC,mDAAwC,EAAC/C,MAAM,CAAC;EAC5E,IAAI8C,mBAAmB,EAAE;IACvB,IAAArB,4CAAgC,EAC9BF,eAAe,EACf1B,MAAM,CAACmD,qBAAqB,EAC5BF,mBACF,CAAC;EACH,CAAC,MAAM;IACL,IAAAT,iDAAqC,EAACd,eAAe,EAAE1B,MAAM,CAACmD,qBAAqB,CAAC;EACtF;EAEA,MAAMC,cAAc,GAAG,IAAAC,8CAAmC,EAAClD,MAAM,CAAC;EAClE,IAAIiD,cAAc,EAAE;IAClB,IAAAxB,4CAAgC,EAC9BF,eAAe,EACf1B,MAAM,CAACsD,yCAAyC,EAChDF,cACF,CAAC;EACH,CAAC,MAAM;IACL,IAAAZ,iDAAqC,EACnCd,eAAe,EACf1B,MAAM,CAACsD,yCACT,CAAC;EACH;EAEA,OAAO,MAAMC,sBAAsB,CAAC/C,WAAW,EAAEL,MAAM,EAAEsB,eAAe,CAAC;AAC3E;AAEO,eAAe8B,sBAAsBA,CAC1C/C,WAAmB,EACnBL,MAAgE,EAChEsB,eAAgC,EACN;EAC1B,MAAMC,eAAe,GAAG,IAAAC,qCAAyB,EAACF,eAAe,CAAC;EAElE,MAAMP,cAAc,GAAG,MAAM,IAAAC,yCAA8B,EAACX,WAAW,EAAEL,MAAM,EAAE,SAAS,CAAC;EAC3F,IAAI,CAACe,cAAc,IAAI,IAAAsC,4BAAgB,EAAC9B,eAAe,EAAE1B,MAAM,CAACyD,eAAe,CAAC,GAAG,CAAC,CAAC,EAAE;IACrF,MAAM,IAAIC,KAAK,CACb,2PACF,CAAC;EACH;EACA,IAAIxC,cAAc,EAAE;IAClB,IAAAsB,iDAAqC,EAACd,eAAe,EAAE,uCAAuC,CAAC;IAC/F,IAAAE,4CAAgC,EAC9BF,eAAe,EACf1B,MAAM,CAACyD,eAAe,EACtB,8BACF,CAAC;EACH,CAAC,MAAM;IACL,IAAAjB,iDAAqC,EAACd,eAAe,EAAE1B,MAAM,CAACyD,eAAe,CAAC;IAC9E,IAAAjB,iDAAqC,EAACd,eAAe,EAAE,uCAAuC,CAAC;EACjG;EAEA,OAAOD,eAAe;AACxB", "ignoreList": []}