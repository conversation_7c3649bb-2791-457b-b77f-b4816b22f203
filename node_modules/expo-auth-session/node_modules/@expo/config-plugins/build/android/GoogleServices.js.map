{"version": 3, "file": "GoogleServices.js", "names": ["_path", "data", "_interopRequireDefault", "require", "_androidPlugins", "_withDangerousMod", "_fs", "_warnings", "obj", "__esModule", "default", "DEFAULT_TARGET_PATH", "googleServicesClassPath", "googleServicesPlugin", "googleServicesVersion", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "config", "withProjectBuildGradle", "modResults", "language", "contents", "setClassPath", "addWarningAndroid", "exports", "withApplyPlugin", "withAppBuildGradle", "applyPlugin", "withGoogleServicesFile", "withDangerousMod", "setGoogleServicesFile", "modRequest", "projectRoot", "getGoogleServicesFilePath", "android", "googleServicesFile", "targetPath", "partialSourcePath", "completeSourcePath", "path", "resolve", "destinationPath", "copyFilePathToPathAsync", "e", "console", "log", "Error", "buildGradle", "includes", "replace", "appBuildGradle", "pattern", "RegExp", "match"], "sources": ["../../src/android/GoogleServices.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport path from 'path';\n\nimport { ConfigPlugin } from '../Plugin.types';\nimport { withAppBuildGradle, withProjectBuildGradle } from '../plugins/android-plugins';\nimport { withDangerousMod } from '../plugins/withDangerousMod';\nimport { copyFilePathToPathAsync } from '../utils/fs';\nimport { addWarningAndroid } from '../utils/warnings';\n\nconst DEFAULT_TARGET_PATH = './android/app/google-services.json';\n\nconst googleServicesClassPath = 'com.google.gms:google-services';\nconst googleServicesPlugin = 'com.google.gms.google-services';\n\n// NOTE(brentvatne): This may be annoying to keep up to date...\nconst googleServicesVersion = '4.4.1';\n\nexport const withClassPath: ConfigPlugin = (config) => {\n  return withProjectBuildGradle(config, (config) => {\n    if (config.modResults.language === 'groovy') {\n      config.modResults.contents = setClassPath(config, config.modResults.contents);\n    } else {\n      addWarningAndroid(\n        'android.googleServicesFile',\n        `Cannot automatically configure project build.gradle if it's not groovy`\n      );\n    }\n    return config;\n  });\n};\n\nexport const withApplyPlugin: ConfigPlugin = (config) => {\n  return withAppBuildGradle(config, (config) => {\n    if (config.modResults.language === 'groovy') {\n      config.modResults.contents = applyPlugin(config, config.modResults.contents);\n    } else {\n      addWarningAndroid(\n        'android.googleServicesFile',\n        `Cannot automatically configure app build.gradle if it's not groovy`\n      );\n    }\n    return config;\n  });\n};\n\n/**\n * Add `google-services.json` to project\n */\nexport const withGoogleServicesFile: ConfigPlugin = (config) => {\n  return withDangerousMod(config, [\n    'android',\n    async (config) => {\n      await setGoogleServicesFile(config, config.modRequest.projectRoot);\n      return config;\n    },\n  ]);\n};\n\nexport function getGoogleServicesFilePath(config: Pick<ExpoConfig, 'android'>) {\n  return config.android?.googleServicesFile ?? null;\n}\n\nexport async function setGoogleServicesFile(\n  config: Pick<ExpoConfig, 'android'>,\n  projectRoot: string,\n  targetPath: string = DEFAULT_TARGET_PATH\n) {\n  const partialSourcePath = getGoogleServicesFilePath(config);\n  if (!partialSourcePath) {\n    return false;\n  }\n\n  const completeSourcePath = path.resolve(projectRoot, partialSourcePath);\n  const destinationPath = path.resolve(projectRoot, targetPath);\n\n  try {\n    await copyFilePathToPathAsync(completeSourcePath, destinationPath);\n  } catch (e) {\n    console.log(e);\n    throw new Error(\n      `Cannot copy google-services.json from ${completeSourcePath} to ${destinationPath}. Please make sure the source and destination paths exist.`\n    );\n  }\n  return true;\n}\n\n/**\n * Adding the Google Services plugin\n * NOTE(brentvatne): string replacement is a fragile approach! we need a\n * better solution than this.\n */\nexport function setClassPath(config: Pick<ExpoConfig, 'android'>, buildGradle: string) {\n  const googleServicesFile = getGoogleServicesFilePath(config);\n  if (!googleServicesFile) {\n    return buildGradle;\n  }\n\n  if (buildGradle.includes(googleServicesClassPath)) {\n    return buildGradle;\n  }\n\n  //\n  return buildGradle.replace(\n    /dependencies\\s?{/,\n    `dependencies {\n        classpath '${googleServicesClassPath}:${googleServicesVersion}'`\n  );\n}\n\nexport function applyPlugin(config: Pick<ExpoConfig, 'android'>, appBuildGradle: string) {\n  const googleServicesFile = getGoogleServicesFilePath(config);\n  if (!googleServicesFile) {\n    return appBuildGradle;\n  }\n\n  // Make sure the project does not have the plugin already\n  const pattern = new RegExp(`apply\\\\s+plugin:\\\\s+['\"]${googleServicesPlugin}['\"]`);\n  if (appBuildGradle.match(pattern)) {\n    return appBuildGradle;\n  }\n\n  // Add it to the end of the file\n  return appBuildGradle + `\\napply plugin: '${googleServicesPlugin}'`;\n}\n"], "mappings": ";;;;;;;;;;AACA,SAAAA,MAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,KAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAG,gBAAA;EAAA,MAAAH,IAAA,GAAAE,OAAA;EAAAC,eAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,kBAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,iBAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,IAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,GAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,UAAA;EAAA,MAAAN,IAAA,GAAAE,OAAA;EAAAI,SAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAsD,SAAAC,uBAAAM,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEtD,MAAMG,mBAAmB,GAAG,oCAAoC;AAEhE,MAAMC,uBAAuB,GAAG,gCAAgC;AAChE,MAAMC,oBAAoB,GAAG,gCAAgC;;AAE7D;AACA,MAAMC,qBAAqB,GAAG,OAAO;AAE9B,MAAMC,aAA2B,GAAIC,MAAM,IAAK;EACrD,OAAO,IAAAC,wCAAsB,EAACD,MAAM,EAAGA,MAAM,IAAK;IAChD,IAAIA,MAAM,CAACE,UAAU,CAACC,QAAQ,KAAK,QAAQ,EAAE;MAC3CH,MAAM,CAACE,UAAU,CAACE,QAAQ,GAAGC,YAAY,CAACL,MAAM,EAAEA,MAAM,CAACE,UAAU,CAACE,QAAQ,CAAC;IAC/E,CAAC,MAAM;MACL,IAAAE,6BAAiB,EACf,4BAA4B,EAC5B,wEACF,CAAC;IACH;IACA,OAAON,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACO,OAAA,CAAAR,aAAA,GAAAA,aAAA;AAEK,MAAMS,eAA6B,GAAIR,MAAM,IAAK;EACvD,OAAO,IAAAS,oCAAkB,EAACT,MAAM,EAAGA,MAAM,IAAK;IAC5C,IAAIA,MAAM,CAACE,UAAU,CAACC,QAAQ,KAAK,QAAQ,EAAE;MAC3CH,MAAM,CAACE,UAAU,CAACE,QAAQ,GAAGM,WAAW,CAACV,MAAM,EAAEA,MAAM,CAACE,UAAU,CAACE,QAAQ,CAAC;IAC9E,CAAC,MAAM;MACL,IAAAE,6BAAiB,EACf,4BAA4B,EAC5B,oEACF,CAAC;IACH;IACA,OAAON,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AAFAO,OAAA,CAAAC,eAAA,GAAAA,eAAA;AAGO,MAAMG,sBAAoC,GAAIX,MAAM,IAAK;EAC9D,OAAO,IAAAY,oCAAgB,EAACZ,MAAM,EAAE,CAC9B,SAAS,EACT,MAAOA,MAAM,IAAK;IAChB,MAAMa,qBAAqB,CAACb,MAAM,EAAEA,MAAM,CAACc,UAAU,CAACC,WAAW,CAAC;IAClE,OAAOf,MAAM;EACf,CAAC,CACF,CAAC;AACJ,CAAC;AAACO,OAAA,CAAAI,sBAAA,GAAAA,sBAAA;AAEK,SAASK,yBAAyBA,CAAChB,MAAmC,EAAE;EAC7E,OAAOA,MAAM,CAACiB,OAAO,EAAEC,kBAAkB,IAAI,IAAI;AACnD;AAEO,eAAeL,qBAAqBA,CACzCb,MAAmC,EACnCe,WAAmB,EACnBI,UAAkB,GAAGxB,mBAAmB,EACxC;EACA,MAAMyB,iBAAiB,GAAGJ,yBAAyB,CAAChB,MAAM,CAAC;EAC3D,IAAI,CAACoB,iBAAiB,EAAE;IACtB,OAAO,KAAK;EACd;EAEA,MAAMC,kBAAkB,GAAGC,eAAI,CAACC,OAAO,CAACR,WAAW,EAAEK,iBAAiB,CAAC;EACvE,MAAMI,eAAe,GAAGF,eAAI,CAACC,OAAO,CAACR,WAAW,EAAEI,UAAU,CAAC;EAE7D,IAAI;IACF,MAAM,IAAAM,6BAAuB,EAACJ,kBAAkB,EAAEG,eAAe,CAAC;EACpE,CAAC,CAAC,OAAOE,CAAC,EAAE;IACVC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;IACd,MAAM,IAAIG,KAAK,CACb,yCAAyCR,kBAAkB,OAAOG,eAAe,4DACnF,CAAC;EACH;EACA,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASnB,YAAYA,CAACL,MAAmC,EAAE8B,WAAmB,EAAE;EACrF,MAAMZ,kBAAkB,GAAGF,yBAAyB,CAAChB,MAAM,CAAC;EAC5D,IAAI,CAACkB,kBAAkB,EAAE;IACvB,OAAOY,WAAW;EACpB;EAEA,IAAIA,WAAW,CAACC,QAAQ,CAACnC,uBAAuB,CAAC,EAAE;IACjD,OAAOkC,WAAW;EACpB;;EAEA;EACA,OAAOA,WAAW,CAACE,OAAO,CACxB,kBAAkB,EAClB;AACJ,qBAAqBpC,uBAAuB,IAAIE,qBAAqB,GACnE,CAAC;AACH;AAEO,SAASY,WAAWA,CAACV,MAAmC,EAAEiC,cAAsB,EAAE;EACvF,MAAMf,kBAAkB,GAAGF,yBAAyB,CAAChB,MAAM,CAAC;EAC5D,IAAI,CAACkB,kBAAkB,EAAE;IACvB,OAAOe,cAAc;EACvB;;EAEA;EACA,MAAMC,OAAO,GAAG,IAAIC,MAAM,CAAC,2BAA2BtC,oBAAoB,MAAM,CAAC;EACjF,IAAIoC,cAAc,CAACG,KAAK,CAACF,OAAO,CAAC,EAAE;IACjC,OAAOD,cAAc;EACvB;;EAEA;EACA,OAAOA,cAAc,GAAG,oBAAoBpC,oBAAoB,GAAG;AACrE", "ignoreList": []}