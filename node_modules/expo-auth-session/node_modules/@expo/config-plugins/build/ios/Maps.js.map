{"version": 3, "file": "Maps.js", "names": ["_path", "data", "_interopRequireDefault", "require", "_resolveFrom", "_iosPlugins", "_generateCode", "obj", "__esModule", "default", "debug", "MATCH_INIT", "exports", "withGoogleMapsKey", "createInfoPlistPlugin", "setGoogleMapsApiKey", "withMaps", "config", "<PERSON><PERSON><PERSON><PERSON>", "getGoogleMapsApiKey", "withMapsCocoaPods", "useGoogleMaps", "withGoogleMapsAppDelegate", "ios", "googleMapsApiKey", "GMSApiKey", "infoPlist", "addGoogleMapsAppDelegateImport", "src", "newSrc", "push", "mergeContents", "tag", "join", "anchor", "offset", "comment", "removeGoogleMapsAppDelegateImport", "removeContents", "addGoogleMapsAppDelegateInit", "removeGoogleMapsAppDelegateInit", "addMapsCocoaPods", "removeMapsCocoaPods", "isReactNativeMapsInstalled", "projectRoot", "resolved", "resolveFrom", "silent", "path", "dirname", "isReactNativeMapsAutolinked", "with<PERSON><PERSON><PERSON><PERSON>", "googleMapsPath", "modRequest", "isLinked", "results", "modResults", "contents", "error", "code", "Error", "didMerge", "<PERSON><PERSON><PERSON><PERSON>", "withAppDelegate", "includes", "language"], "sources": ["../../src/ios/Maps.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { ConfigPlugin, InfoPlist } from '../Plugin.types';\nimport { createInfoPlistPlugin, withAppDelegate, withPodfile } from '../plugins/ios-plugins';\nimport { mergeContents, MergeResults, removeContents } from '../utils/generateCode';\n\nconst debug = require('debug')('expo:config-plugins:ios:maps') as typeof console.log;\n\nexport const MATCH_INIT =\n  /-\\s*\\(BOOL\\)\\s*application:\\s*\\(UIApplication\\s*\\*\\s*\\)\\s*\\w+\\s+didFinishLaunchingWithOptions:/g;\n\nconst withGoogleMapsKey = createInfoPlistPlugin(setGoogleMapsApiKey, 'withGoogleMapsKey');\n\nexport const withMaps: ConfigPlugin = (config) => {\n  config = withGoogleMapsKey(config);\n\n  const apiKey = getGoogleMapsApiKey(config);\n  // Technically adds react-native-maps (Apple maps) and google maps.\n\n  debug('Google Maps API Key:', apiKey);\n  config = withMapsCocoaPods(config, { useGoogleMaps: !!apiKey });\n\n  // Adds/Removes AppDelegate setup for Google Maps API on iOS\n  config = withGoogleMapsAppDelegate(config, { apiKey });\n\n  return config;\n};\n\nexport function getGoogleMapsApiKey(config: Pick<ExpoConfig, 'ios'>) {\n  return config.ios?.config?.googleMapsApiKey ?? null;\n}\n\nexport function setGoogleMapsApiKey(\n  config: Pick<ExpoConfig, 'ios'>,\n  { GMSApiKey, ...infoPlist }: InfoPlist\n): InfoPlist {\n  const apiKey = getGoogleMapsApiKey(config);\n\n  if (apiKey === null) {\n    return infoPlist;\n  }\n\n  return {\n    ...infoPlist,\n    GMSApiKey: apiKey,\n  };\n}\n\nexport function addGoogleMapsAppDelegateImport(src: string): MergeResults {\n  const newSrc = [];\n  newSrc.push(\n    '#if __has_include(<GoogleMaps/GoogleMaps.h>)',\n    '#import <GoogleMaps/GoogleMaps.h>',\n    '#endif'\n  );\n\n  return mergeContents({\n    tag: 'react-native-maps-import',\n    src,\n    newSrc: newSrc.join('\\n'),\n    anchor: /#import \"AppDelegate\\.h\"/,\n    offset: 1,\n    comment: '//',\n  });\n}\n\nexport function removeGoogleMapsAppDelegateImport(src: string): MergeResults {\n  return removeContents({\n    tag: 'react-native-maps-import',\n    src,\n  });\n}\n\nexport function addGoogleMapsAppDelegateInit(src: string, apiKey: string): MergeResults {\n  const newSrc = [];\n  newSrc.push(\n    '#if __has_include(<GoogleMaps/GoogleMaps.h>)',\n    `  [GMSServices provideAPIKey:@\"${apiKey}\"];`,\n    '#endif'\n  );\n\n  return mergeContents({\n    tag: 'react-native-maps-init',\n    src,\n    newSrc: newSrc.join('\\n'),\n    anchor: MATCH_INIT,\n    offset: 2,\n    comment: '//',\n  });\n}\n\nexport function removeGoogleMapsAppDelegateInit(src: string): MergeResults {\n  return removeContents({\n    tag: 'react-native-maps-init',\n    src,\n  });\n}\n\n/**\n * @param src The contents of the Podfile.\n * @returns Podfile with Google Maps added.\n */\nexport function addMapsCocoaPods(src: string): MergeResults {\n  return mergeContents({\n    tag: 'react-native-maps',\n    src,\n    newSrc: `  pod 'react-native-google-maps', path: File.dirname(\\`node --print \"require.resolve('react-native-maps/package.json')\"\\`)`,\n    anchor: /use_native_modules/,\n    offset: 0,\n    comment: '#',\n  });\n}\n\nexport function removeMapsCocoaPods(src: string): MergeResults {\n  return removeContents({\n    tag: 'react-native-maps',\n    src,\n  });\n}\n\nfunction isReactNativeMapsInstalled(projectRoot: string): string | null {\n  const resolved = resolveFrom.silent(projectRoot, 'react-native-maps/package.json');\n  return resolved ? path.dirname(resolved) : null;\n}\n\nfunction isReactNativeMapsAutolinked(config: Pick<ExpoConfig, '_internal'>): boolean {\n  // Only add the native code changes if we know that the package is going to be linked natively.\n  // This is specifically for monorepo support where one app might have react-native-maps (adding it to the node_modules)\n  // but another app will not have it installed in the package.json, causing it to not be linked natively.\n  // This workaround only exists because react-native-maps doesn't have a config plugin vendored in the package.\n\n  // TODO: `react-native-maps` doesn't use Expo autolinking so we cannot safely disable the module.\n  return true;\n\n  // return (\n  //   !config._internal?.autolinkedModules ||\n  //   config._internal.autolinkedModules.includes('react-native-maps')\n  // );\n}\n\nconst withMapsCocoaPods: ConfigPlugin<{ useGoogleMaps: boolean }> = (config, { useGoogleMaps }) => {\n  return withPodfile(config, async (config) => {\n    // Only add the block if react-native-maps is installed in the project (best effort).\n    // Generally prebuild runs after a yarn install so this should always work as expected.\n    const googleMapsPath = isReactNativeMapsInstalled(config.modRequest.projectRoot);\n    const isLinked = isReactNativeMapsAutolinked(config);\n    debug('Is Expo Autolinked:', isLinked);\n    debug('react-native-maps path:', googleMapsPath);\n\n    let results: MergeResults;\n\n    if (isLinked && googleMapsPath && useGoogleMaps) {\n      try {\n        results = addMapsCocoaPods(config.modResults.contents);\n      } catch (error: any) {\n        if (error.code === 'ERR_NO_MATCH') {\n          throw new Error(\n            `Cannot add react-native-maps to the project's ios/Podfile because it's malformed. Please report this with a copy of your project Podfile.`\n          );\n        }\n        throw error;\n      }\n    } else {\n      // If the package is no longer installed, then remove the block.\n      results = removeMapsCocoaPods(config.modResults.contents);\n    }\n\n    if (results.didMerge || results.didClear) {\n      config.modResults.contents = results.contents;\n    }\n\n    return config;\n  });\n};\n\nconst withGoogleMapsAppDelegate: ConfigPlugin<{ apiKey: string | null }> = (config, { apiKey }) => {\n  return withAppDelegate(config, (config) => {\n    if (['objc', 'objcpp'].includes(config.modResults.language)) {\n      if (\n        apiKey &&\n        isReactNativeMapsAutolinked(config) &&\n        isReactNativeMapsInstalled(config.modRequest.projectRoot)\n      ) {\n        try {\n          config.modResults.contents = addGoogleMapsAppDelegateImport(\n            config.modResults.contents\n          ).contents;\n          config.modResults.contents = addGoogleMapsAppDelegateInit(\n            config.modResults.contents,\n            apiKey\n          ).contents;\n        } catch (error: any) {\n          if (error.code === 'ERR_NO_MATCH') {\n            throw new Error(\n              `Cannot add Google Maps to the project's AppDelegate because it's malformed. Please report this with a copy of your project AppDelegate.`\n            );\n          }\n          throw error;\n        }\n      } else {\n        config.modResults.contents = removeGoogleMapsAppDelegateImport(\n          config.modResults.contents\n        ).contents;\n        config.modResults.contents = removeGoogleMapsAppDelegateInit(\n          config.modResults.contents\n        ).contents;\n      }\n    } else {\n      throw new Error(\n        `Cannot setup Google Maps because the project AppDelegate is not a supported language: ${config.modResults.language}`\n      );\n    }\n    return config;\n  });\n};\n"], "mappings": ";;;;;;;;;;;;;;;AACA,SAAAA,MAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,KAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,aAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,YAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAI,YAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,WAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,cAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,aAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAoF,SAAAC,uBAAAK,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEpF,MAAMG,KAAK,GAAGP,OAAO,CAAC,OAAO,CAAC,CAAC,8BAA8B,CAAuB;AAE7E,MAAMQ,UAAU,GAAAC,OAAA,CAAAD,UAAA,GACrB,iGAAiG;AAEnG,MAAME,iBAAiB,GAAG,IAAAC,mCAAqB,EAACC,mBAAmB,EAAE,mBAAmB,CAAC;AAElF,MAAMC,QAAsB,GAAIC,MAAM,IAAK;EAChDA,MAAM,GAAGJ,iBAAiB,CAACI,MAAM,CAAC;EAElC,MAAMC,MAAM,GAAGC,mBAAmB,CAACF,MAAM,CAAC;EAC1C;;EAEAP,KAAK,CAAC,sBAAsB,EAAEQ,MAAM,CAAC;EACrCD,MAAM,GAAGG,iBAAiB,CAACH,MAAM,EAAE;IAAEI,aAAa,EAAE,CAAC,CAACH;EAAO,CAAC,CAAC;;EAE/D;EACAD,MAAM,GAAGK,yBAAyB,CAACL,MAAM,EAAE;IAAEC;EAAO,CAAC,CAAC;EAEtD,OAAOD,MAAM;AACf,CAAC;AAACL,OAAA,CAAAI,QAAA,GAAAA,QAAA;AAEK,SAASG,mBAAmBA,CAACF,MAA+B,EAAE;EACnE,OAAOA,MAAM,CAACM,GAAG,EAAEN,MAAM,EAAEO,gBAAgB,IAAI,IAAI;AACrD;AAEO,SAAST,mBAAmBA,CACjCE,MAA+B,EAC/B;EAAEQ,SAAS;EAAE,GAAGC;AAAqB,CAAC,EAC3B;EACX,MAAMR,MAAM,GAAGC,mBAAmB,CAACF,MAAM,CAAC;EAE1C,IAAIC,MAAM,KAAK,IAAI,EAAE;IACnB,OAAOQ,SAAS;EAClB;EAEA,OAAO;IACL,GAAGA,SAAS;IACZD,SAAS,EAAEP;EACb,CAAC;AACH;AAEO,SAASS,8BAA8BA,CAACC,GAAW,EAAgB;EACxE,MAAMC,MAAM,GAAG,EAAE;EACjBA,MAAM,CAACC,IAAI,CACT,8CAA8C,EAC9C,mCAAmC,EACnC,QACF,CAAC;EAED,OAAO,IAAAC,6BAAa,EAAC;IACnBC,GAAG,EAAE,0BAA0B;IAC/BJ,GAAG;IACHC,MAAM,EAAEA,MAAM,CAACI,IAAI,CAAC,IAAI,CAAC;IACzBC,MAAM,EAAE,0BAA0B;IAClCC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE;EACX,CAAC,CAAC;AACJ;AAEO,SAASC,iCAAiCA,CAACT,GAAW,EAAgB;EAC3E,OAAO,IAAAU,8BAAc,EAAC;IACpBN,GAAG,EAAE,0BAA0B;IAC/BJ;EACF,CAAC,CAAC;AACJ;AAEO,SAASW,4BAA4BA,CAACX,GAAW,EAAEV,MAAc,EAAgB;EACtF,MAAMW,MAAM,GAAG,EAAE;EACjBA,MAAM,CAACC,IAAI,CACT,8CAA8C,EAC9C,kCAAkCZ,MAAM,KAAK,EAC7C,QACF,CAAC;EAED,OAAO,IAAAa,6BAAa,EAAC;IACnBC,GAAG,EAAE,wBAAwB;IAC7BJ,GAAG;IACHC,MAAM,EAAEA,MAAM,CAACI,IAAI,CAAC,IAAI,CAAC;IACzBC,MAAM,EAAEvB,UAAU;IAClBwB,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE;EACX,CAAC,CAAC;AACJ;AAEO,SAASI,+BAA+BA,CAACZ,GAAW,EAAgB;EACzE,OAAO,IAAAU,8BAAc,EAAC;IACpBN,GAAG,EAAE,wBAAwB;IAC7BJ;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACO,SAASa,gBAAgBA,CAACb,GAAW,EAAgB;EAC1D,OAAO,IAAAG,6BAAa,EAAC;IACnBC,GAAG,EAAE,mBAAmB;IACxBJ,GAAG;IACHC,MAAM,EAAE,4HAA4H;IACpIK,MAAM,EAAE,oBAAoB;IAC5BC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE;EACX,CAAC,CAAC;AACJ;AAEO,SAASM,mBAAmBA,CAACd,GAAW,EAAgB;EAC7D,OAAO,IAAAU,8BAAc,EAAC;IACpBN,GAAG,EAAE,mBAAmB;IACxBJ;EACF,CAAC,CAAC;AACJ;AAEA,SAASe,0BAA0BA,CAACC,WAAmB,EAAiB;EACtE,MAAMC,QAAQ,GAAGC,sBAAW,CAACC,MAAM,CAACH,WAAW,EAAE,gCAAgC,CAAC;EAClF,OAAOC,QAAQ,GAAGG,eAAI,CAACC,OAAO,CAACJ,QAAQ,CAAC,GAAG,IAAI;AACjD;AAEA,SAASK,2BAA2BA,CAACjC,MAAqC,EAAW;EACnF;EACA;EACA;EACA;;EAEA;EACA,OAAO,IAAI;;EAEX;EACA;EACA;EACA;AACF;AAEA,MAAMG,iBAA2D,GAAGA,CAACH,MAAM,EAAE;EAAEI;AAAc,CAAC,KAAK;EACjG,OAAO,IAAA8B,yBAAW,EAAClC,MAAM,EAAE,MAAOA,MAAM,IAAK;IAC3C;IACA;IACA,MAAMmC,cAAc,GAAGT,0BAA0B,CAAC1B,MAAM,CAACoC,UAAU,CAACT,WAAW,CAAC;IAChF,MAAMU,QAAQ,GAAGJ,2BAA2B,CAACjC,MAAM,CAAC;IACpDP,KAAK,CAAC,qBAAqB,EAAE4C,QAAQ,CAAC;IACtC5C,KAAK,CAAC,yBAAyB,EAAE0C,cAAc,CAAC;IAEhD,IAAIG,OAAqB;IAEzB,IAAID,QAAQ,IAAIF,cAAc,IAAI/B,aAAa,EAAE;MAC/C,IAAI;QACFkC,OAAO,GAAGd,gBAAgB,CAACxB,MAAM,CAACuC,UAAU,CAACC,QAAQ,CAAC;MACxD,CAAC,CAAC,OAAOC,KAAU,EAAE;QACnB,IAAIA,KAAK,CAACC,IAAI,KAAK,cAAc,EAAE;UACjC,MAAM,IAAIC,KAAK,CACb,2IACF,CAAC;QACH;QACA,MAAMF,KAAK;MACb;IACF,CAAC,MAAM;MACL;MACAH,OAAO,GAAGb,mBAAmB,CAACzB,MAAM,CAACuC,UAAU,CAACC,QAAQ,CAAC;IAC3D;IAEA,IAAIF,OAAO,CAACM,QAAQ,IAAIN,OAAO,CAACO,QAAQ,EAAE;MACxC7C,MAAM,CAACuC,UAAU,CAACC,QAAQ,GAAGF,OAAO,CAACE,QAAQ;IAC/C;IAEA,OAAOxC,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAED,MAAMK,yBAAkE,GAAGA,CAACL,MAAM,EAAE;EAAEC;AAAO,CAAC,KAAK;EACjG,OAAO,IAAA6C,6BAAe,EAAC9C,MAAM,EAAGA,MAAM,IAAK;IACzC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC+C,QAAQ,CAAC/C,MAAM,CAACuC,UAAU,CAACS,QAAQ,CAAC,EAAE;MAC3D,IACE/C,MAAM,IACNgC,2BAA2B,CAACjC,MAAM,CAAC,IACnC0B,0BAA0B,CAAC1B,MAAM,CAACoC,UAAU,CAACT,WAAW,CAAC,EACzD;QACA,IAAI;UACF3B,MAAM,CAACuC,UAAU,CAACC,QAAQ,GAAG9B,8BAA8B,CACzDV,MAAM,CAACuC,UAAU,CAACC,QACpB,CAAC,CAACA,QAAQ;UACVxC,MAAM,CAACuC,UAAU,CAACC,QAAQ,GAAGlB,4BAA4B,CACvDtB,MAAM,CAACuC,UAAU,CAACC,QAAQ,EAC1BvC,MACF,CAAC,CAACuC,QAAQ;QACZ,CAAC,CAAC,OAAOC,KAAU,EAAE;UACnB,IAAIA,KAAK,CAACC,IAAI,KAAK,cAAc,EAAE;YACjC,MAAM,IAAIC,KAAK,CACb,yIACF,CAAC;UACH;UACA,MAAMF,KAAK;QACb;MACF,CAAC,MAAM;QACLzC,MAAM,CAACuC,UAAU,CAACC,QAAQ,GAAGpB,iCAAiC,CAC5DpB,MAAM,CAACuC,UAAU,CAACC,QACpB,CAAC,CAACA,QAAQ;QACVxC,MAAM,CAACuC,UAAU,CAACC,QAAQ,GAAGjB,+BAA+B,CAC1DvB,MAAM,CAACuC,UAAU,CAACC,QACpB,CAAC,CAACA,QAAQ;MACZ;IACF,CAAC,MAAM;MACL,MAAM,IAAIG,KAAK,CACb,yFAAyF3C,MAAM,CAACuC,UAAU,CAACS,QAAQ,EACrH,CAAC;IACH;IACA,OAAOhD,MAAM;EACf,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}