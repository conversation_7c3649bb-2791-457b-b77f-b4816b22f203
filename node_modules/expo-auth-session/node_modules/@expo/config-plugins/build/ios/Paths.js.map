{"version": 3, "file": "Paths.js", "names": ["_fs", "data", "require", "_glob", "path", "_interopRequireWildcard", "Entitlements", "_errors", "_warnings", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "ignoredPaths", "getAppDelegateHeaderFilePath", "projectRoot", "using", "extra", "globSync", "absolute", "cwd", "ignore", "UnexpectedError", "length", "warnMultipleFiles", "tag", "fileName", "getAppDelegateFilePath", "getAppDelegateObjcHeaderFilePath", "getPodfilePath", "getLanguage", "filePath", "extension", "extname", "basename", "getFileInfo", "normalize", "contents", "readFileSync", "language", "getAppDelegate", "getSourceRoot", "appDelegate", "dirname", "findSchemePaths", "findSchemeNames", "schemePaths", "map", "schemePath", "parse", "name", "getAllXcodeProjectPaths", "iosFolder", "pbxproj<PERSON><PERSON><PERSON>", "filter", "project", "test", "sort", "b", "isAInIos", "isBInIos", "value", "join", "getXcodeProjectPath", "getAllPBXProjectPaths", "projectPaths", "paths", "existsSync", "getPBXProjectPath", "getAllInfoPlistPaths", "getInfoPlistPath", "getAllEntitlementsPaths", "getEntitlementsPath", "getSupportingPath", "resolve", "getExpoPlistPath", "supporting<PERSON>ath", "usingPath", "relative", "extraPaths", "v", "addWarningIOS", "JSON", "stringify"], "sources": ["../../src/ios/Paths.ts"], "sourcesContent": ["import { existsSync, readFileSync } from 'fs';\nimport { sync as globSync } from 'glob';\nimport * as path from 'path';\n\nimport * as Entitlements from './Entitlements';\nimport { UnexpectedError } from '../utils/errors';\nimport { addWarningIOS } from '../utils/warnings';\n\nconst ignoredPaths = ['**/@(Carthage|Pods|vendor|node_modules)/**'];\n\ninterface ProjectFile<L extends string = string> {\n  path: string;\n  language: L;\n  contents: string;\n}\n\ntype AppleLanguage = 'objc' | 'objcpp' | 'swift' | 'rb';\n\nexport type PodfileProjectFile = ProjectFile<'rb'>;\nexport type AppDelegateProjectFile = ProjectFile<AppleLanguage>;\n\nexport function getAppDelegateHeaderFilePath(projectRoot: string): string {\n  const [using, ...extra] = globSync('ios/*/AppDelegate.h', {\n    absolute: true,\n    cwd: projectRoot,\n    ignore: ignoredPaths,\n  });\n\n  if (!using) {\n    throw new UnexpectedError(\n      `Could not locate a valid AppDelegate header at root: \"${projectRoot}\"`\n    );\n  }\n\n  if (extra.length) {\n    warnMultipleFiles({\n      tag: 'app-delegate-header',\n      fileName: 'AppDelegate',\n      projectRoot,\n      using,\n      extra,\n    });\n  }\n\n  return using;\n}\n\nexport function getAppDelegateFilePath(projectRoot: string): string {\n  const [using, ...extra] = globSync('ios/*/AppDelegate.@(m|mm|swift)', {\n    absolute: true,\n    cwd: projectRoot,\n    ignore: ignoredPaths,\n  });\n\n  if (!using) {\n    throw new UnexpectedError(`Could not locate a valid AppDelegate at root: \"${projectRoot}\"`);\n  }\n\n  if (extra.length) {\n    warnMultipleFiles({\n      tag: 'app-delegate',\n      fileName: 'AppDelegate',\n      projectRoot,\n      using,\n      extra,\n    });\n  }\n\n  return using;\n}\n\nexport function getAppDelegateObjcHeaderFilePath(projectRoot: string): string {\n  const [using, ...extra] = globSync('ios/*/AppDelegate.h', {\n    absolute: true,\n    cwd: projectRoot,\n    ignore: ignoredPaths,\n  });\n\n  if (!using) {\n    throw new UnexpectedError(`Could not locate a valid AppDelegate.h at root: \"${projectRoot}\"`);\n  }\n\n  if (extra.length) {\n    warnMultipleFiles({\n      tag: 'app-delegate-objc-header',\n      fileName: 'AppDelegate.h',\n      projectRoot,\n      using,\n      extra,\n    });\n  }\n\n  return using;\n}\n\nexport function getPodfilePath(projectRoot: string): string {\n  const [using, ...extra] = globSync('ios/Podfile', {\n    absolute: true,\n    cwd: projectRoot,\n    ignore: ignoredPaths,\n  });\n\n  if (!using) {\n    throw new UnexpectedError(`Could not locate a valid Podfile at root: \"${projectRoot}\"`);\n  }\n\n  if (extra.length) {\n    warnMultipleFiles({\n      tag: 'podfile',\n      fileName: 'Podfile',\n      projectRoot,\n      using,\n      extra,\n    });\n  }\n\n  return using;\n}\n\nfunction getLanguage(filePath: string): AppleLanguage {\n  const extension = path.extname(filePath);\n  if (!extension && path.basename(filePath) === 'Podfile') {\n    return 'rb';\n  }\n  switch (extension) {\n    case '.mm':\n      return 'objcpp';\n    case '.m':\n    case '.h':\n      return 'objc';\n    case '.swift':\n      return 'swift';\n    default:\n      throw new UnexpectedError(`Unexpected iOS file extension: ${extension}`);\n  }\n}\n\nexport function getFileInfo(filePath: string) {\n  return {\n    path: path.normalize(filePath),\n    contents: readFileSync(filePath, 'utf8'),\n    language: getLanguage(filePath),\n  };\n}\n\nexport function getAppDelegate(projectRoot: string): AppDelegateProjectFile {\n  const filePath = getAppDelegateFilePath(projectRoot);\n  return getFileInfo(filePath);\n}\n\nexport function getSourceRoot(projectRoot: string): string {\n  const appDelegate = getAppDelegate(projectRoot);\n  return path.dirname(appDelegate.path);\n}\n\nexport function findSchemePaths(projectRoot: string): string[] {\n  return globSync('ios/*.xcodeproj/xcshareddata/xcschemes/*.xcscheme', {\n    absolute: true,\n    cwd: projectRoot,\n    ignore: ignoredPaths,\n  });\n}\n\nexport function findSchemeNames(projectRoot: string): string[] {\n  const schemePaths = findSchemePaths(projectRoot);\n  return schemePaths.map((schemePath) => path.parse(schemePath).name);\n}\n\nexport function getAllXcodeProjectPaths(projectRoot: string): string[] {\n  const iosFolder = 'ios';\n  const pbxprojPaths = globSync('ios/**/*.xcodeproj', { cwd: projectRoot, ignore: ignoredPaths })\n    .filter(\n      (project) => !/test|example|sample/i.test(project) || path.dirname(project) === iosFolder\n    )\n    // sort alphabetically to ensure this works the same across different devices (Fail in CI (linux) without this)\n    .sort()\n    .sort((a, b) => {\n      const isAInIos = path.dirname(a) === iosFolder;\n      const isBInIos = path.dirname(b) === iosFolder;\n      // preserve previous sort order\n      if ((isAInIos && isBInIos) || (!isAInIos && !isBInIos)) {\n        return 0;\n      }\n      return isAInIos ? -1 : 1;\n    });\n\n  if (!pbxprojPaths.length) {\n    throw new UnexpectedError(\n      `Failed to locate the ios/*.xcodeproj files relative to path \"${projectRoot}\".`\n    );\n  }\n  return pbxprojPaths.map((value) => path.join(projectRoot, value));\n}\n\n/**\n * Get the pbxproj for the given path\n */\nexport function getXcodeProjectPath(projectRoot: string): string {\n  const [using, ...extra] = getAllXcodeProjectPaths(projectRoot);\n\n  if (extra.length) {\n    warnMultipleFiles({\n      tag: 'xcodeproj',\n      fileName: '*.xcodeproj',\n      projectRoot,\n      using,\n      extra,\n    });\n  }\n\n  return using;\n}\n\nexport function getAllPBXProjectPaths(projectRoot: string): string[] {\n  const projectPaths = getAllXcodeProjectPaths(projectRoot);\n  const paths = projectPaths\n    .map((value) => path.join(value, 'project.pbxproj'))\n    .filter((value) => existsSync(value));\n\n  if (!paths.length) {\n    throw new UnexpectedError(\n      `Failed to locate the ios/*.xcodeproj/project.pbxproj files relative to path \"${projectRoot}\".`\n    );\n  }\n  return paths;\n}\n\nexport function getPBXProjectPath(projectRoot: string): string {\n  const [using, ...extra] = getAllPBXProjectPaths(projectRoot);\n\n  if (extra.length) {\n    warnMultipleFiles({\n      tag: 'project-pbxproj',\n      fileName: 'project.pbxproj',\n      projectRoot,\n      using,\n      extra,\n    });\n  }\n\n  return using;\n}\n\nexport function getAllInfoPlistPaths(projectRoot: string): string[] {\n  const paths = globSync('ios/*/Info.plist', {\n    absolute: true,\n    cwd: projectRoot,\n    ignore: ignoredPaths,\n  }).sort(\n    // longer name means more suffixes, we want the shortest possible one to be first.\n    (a, b) => a.length - b.length\n  );\n\n  if (!paths.length) {\n    throw new UnexpectedError(\n      `Failed to locate Info.plist files relative to path \"${projectRoot}\".`\n    );\n  }\n  return paths;\n}\n\nexport function getInfoPlistPath(projectRoot: string): string {\n  const [using, ...extra] = getAllInfoPlistPaths(projectRoot);\n\n  if (extra.length) {\n    warnMultipleFiles({\n      tag: 'info-plist',\n      fileName: 'Info.plist',\n      projectRoot,\n      using,\n      extra,\n    });\n  }\n\n  return using;\n}\n\nexport function getAllEntitlementsPaths(projectRoot: string): string[] {\n  const paths = globSync('ios/*/*.entitlements', {\n    absolute: true,\n    cwd: projectRoot,\n    ignore: ignoredPaths,\n  });\n  return paths;\n}\n\n/**\n * @deprecated: use Entitlements.getEntitlementsPath instead\n */\nexport function getEntitlementsPath(projectRoot: string): string | null {\n  return Entitlements.getEntitlementsPath(projectRoot);\n}\n\nexport function getSupportingPath(projectRoot: string): string {\n  return path.resolve(projectRoot, 'ios', path.basename(getSourceRoot(projectRoot)), 'Supporting');\n}\n\nexport function getExpoPlistPath(projectRoot: string): string {\n  const supportingPath = getSupportingPath(projectRoot);\n  return path.join(supportingPath, 'Expo.plist');\n}\n\nfunction warnMultipleFiles({\n  tag,\n  fileName,\n  projectRoot,\n  using,\n  extra,\n}: {\n  tag: string;\n  fileName: string;\n  projectRoot?: string;\n  using: string;\n  extra: string[];\n}) {\n  const usingPath = projectRoot ? path.relative(projectRoot, using) : using;\n  const extraPaths = projectRoot ? extra.map((v) => path.relative(projectRoot, v)) : extra;\n  addWarningIOS(\n    `paths-${tag}`,\n    `Found multiple ${fileName} file paths, using \"${usingPath}\". Ignored paths: ${JSON.stringify(\n      extraPaths\n    )}`\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,MAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,KAAA;EAAA,MAAAH,IAAA,GAAAI,uBAAA,CAAAH,OAAA;EAAAE,IAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAK,aAAA;EAAA,MAAAL,IAAA,GAAAI,uBAAA,CAAAH,OAAA;EAAAI,YAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,QAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,OAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,UAAA;EAAA,MAAAP,IAAA,GAAAC,OAAA;EAAAM,SAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAkD,SAAAQ,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAL,wBAAAK,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAc,CAAA,SAAAI,CAAA,GAAAR,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAI,CAAA,KAAAA,CAAA,CAAAX,GAAA,IAAAW,CAAA,CAAAC,GAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAI,CAAA,IAAAV,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAgB,GAAA,CAAAnB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAElD,MAAMY,YAAY,GAAG,CAAC,4CAA4C,CAAC;AAa5D,SAASC,4BAA4BA,CAACC,WAAmB,EAAU;EACxE,MAAM,CAACC,KAAK,EAAE,GAAGC,KAAK,CAAC,GAAG,IAAAC,YAAQ,EAAC,qBAAqB,EAAE;IACxDC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAEL,WAAW;IAChBM,MAAM,EAAER;EACV,CAAC,CAAC;EAEF,IAAI,CAACG,KAAK,EAAE;IACV,MAAM,KAAIM,yBAAe,EACvB,yDAAyDP,WAAW,GACtE,CAAC;EACH;EAEA,IAAIE,KAAK,CAACM,MAAM,EAAE;IAChBC,iBAAiB,CAAC;MAChBC,GAAG,EAAE,qBAAqB;MAC1BC,QAAQ,EAAE,aAAa;MACvBX,WAAW;MACXC,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOD,KAAK;AACd;AAEO,SAASW,sBAAsBA,CAACZ,WAAmB,EAAU;EAClE,MAAM,CAACC,KAAK,EAAE,GAAGC,KAAK,CAAC,GAAG,IAAAC,YAAQ,EAAC,iCAAiC,EAAE;IACpEC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAEL,WAAW;IAChBM,MAAM,EAAER;EACV,CAAC,CAAC;EAEF,IAAI,CAACG,KAAK,EAAE;IACV,MAAM,KAAIM,yBAAe,EAAC,kDAAkDP,WAAW,GAAG,CAAC;EAC7F;EAEA,IAAIE,KAAK,CAACM,MAAM,EAAE;IAChBC,iBAAiB,CAAC;MAChBC,GAAG,EAAE,cAAc;MACnBC,QAAQ,EAAE,aAAa;MACvBX,WAAW;MACXC,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOD,KAAK;AACd;AAEO,SAASY,gCAAgCA,CAACb,WAAmB,EAAU;EAC5E,MAAM,CAACC,KAAK,EAAE,GAAGC,KAAK,CAAC,GAAG,IAAAC,YAAQ,EAAC,qBAAqB,EAAE;IACxDC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAEL,WAAW;IAChBM,MAAM,EAAER;EACV,CAAC,CAAC;EAEF,IAAI,CAACG,KAAK,EAAE;IACV,MAAM,KAAIM,yBAAe,EAAC,oDAAoDP,WAAW,GAAG,CAAC;EAC/F;EAEA,IAAIE,KAAK,CAACM,MAAM,EAAE;IAChBC,iBAAiB,CAAC;MAChBC,GAAG,EAAE,0BAA0B;MAC/BC,QAAQ,EAAE,eAAe;MACzBX,WAAW;MACXC,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOD,KAAK;AACd;AAEO,SAASa,cAAcA,CAACd,WAAmB,EAAU;EAC1D,MAAM,CAACC,KAAK,EAAE,GAAGC,KAAK,CAAC,GAAG,IAAAC,YAAQ,EAAC,aAAa,EAAE;IAChDC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAEL,WAAW;IAChBM,MAAM,EAAER;EACV,CAAC,CAAC;EAEF,IAAI,CAACG,KAAK,EAAE;IACV,MAAM,KAAIM,yBAAe,EAAC,8CAA8CP,WAAW,GAAG,CAAC;EACzF;EAEA,IAAIE,KAAK,CAACM,MAAM,EAAE;IAChBC,iBAAiB,CAAC;MAChBC,GAAG,EAAE,SAAS;MACdC,QAAQ,EAAE,SAAS;MACnBX,WAAW;MACXC,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOD,KAAK;AACd;AAEA,SAASc,WAAWA,CAACC,QAAgB,EAAiB;EACpD,MAAMC,SAAS,GAAG7C,IAAI,CAAD,CAAC,CAAC8C,OAAO,CAACF,QAAQ,CAAC;EACxC,IAAI,CAACC,SAAS,IAAI7C,IAAI,CAAD,CAAC,CAAC+C,QAAQ,CAACH,QAAQ,CAAC,KAAK,SAAS,EAAE;IACvD,OAAO,IAAI;EACb;EACA,QAAQC,SAAS;IACf,KAAK,KAAK;MACR,OAAO,QAAQ;IACjB,KAAK,IAAI;IACT,KAAK,IAAI;MACP,OAAO,MAAM;IACf,KAAK,QAAQ;MACX,OAAO,OAAO;IAChB;MACE,MAAM,KAAIV,yBAAe,EAAC,kCAAkCU,SAAS,EAAE,CAAC;EAC5E;AACF;AAEO,SAASG,WAAWA,CAACJ,QAAgB,EAAE;EAC5C,OAAO;IACL5C,IAAI,EAAEA,IAAI,CAAD,CAAC,CAACiD,SAAS,CAACL,QAAQ,CAAC;IAC9BM,QAAQ,EAAE,IAAAC,kBAAY,EAACP,QAAQ,EAAE,MAAM,CAAC;IACxCQ,QAAQ,EAAET,WAAW,CAACC,QAAQ;EAChC,CAAC;AACH;AAEO,SAASS,cAAcA,CAACzB,WAAmB,EAA0B;EAC1E,MAAMgB,QAAQ,GAAGJ,sBAAsB,CAACZ,WAAW,CAAC;EACpD,OAAOoB,WAAW,CAACJ,QAAQ,CAAC;AAC9B;AAEO,SAASU,aAAaA,CAAC1B,WAAmB,EAAU;EACzD,MAAM2B,WAAW,GAAGF,cAAc,CAACzB,WAAW,CAAC;EAC/C,OAAO5B,IAAI,CAAD,CAAC,CAACwD,OAAO,CAACD,WAAW,CAACvD,IAAI,CAAC;AACvC;AAEO,SAASyD,eAAeA,CAAC7B,WAAmB,EAAY;EAC7D,OAAO,IAAAG,YAAQ,EAAC,mDAAmD,EAAE;IACnEC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAEL,WAAW;IAChBM,MAAM,EAAER;EACV,CAAC,CAAC;AACJ;AAEO,SAASgC,eAAeA,CAAC9B,WAAmB,EAAY;EAC7D,MAAM+B,WAAW,GAAGF,eAAe,CAAC7B,WAAW,CAAC;EAChD,OAAO+B,WAAW,CAACC,GAAG,CAAEC,UAAU,IAAK7D,IAAI,CAAD,CAAC,CAAC8D,KAAK,CAACD,UAAU,CAAC,CAACE,IAAI,CAAC;AACrE;AAEO,SAASC,uBAAuBA,CAACpC,WAAmB,EAAY;EACrE,MAAMqC,SAAS,GAAG,KAAK;EACvB,MAAMC,YAAY,GAAG,IAAAnC,YAAQ,EAAC,oBAAoB,EAAE;IAAEE,GAAG,EAAEL,WAAW;IAAEM,MAAM,EAAER;EAAa,CAAC,CAAC,CAC5FyC,MAAM,CACJC,OAAO,IAAK,CAAC,sBAAsB,CAACC,IAAI,CAACD,OAAO,CAAC,IAAIpE,IAAI,CAAD,CAAC,CAACwD,OAAO,CAACY,OAAO,CAAC,KAAKH,SAClF;EACA;EAAA,CACCK,IAAI,CAAC,CAAC,CACNA,IAAI,CAAC,CAACtD,CAAC,EAAEuD,CAAC,KAAK;IACd,MAAMC,QAAQ,GAAGxE,IAAI,CAAD,CAAC,CAACwD,OAAO,CAACxC,CAAC,CAAC,KAAKiD,SAAS;IAC9C,MAAMQ,QAAQ,GAAGzE,IAAI,CAAD,CAAC,CAACwD,OAAO,CAACe,CAAC,CAAC,KAAKN,SAAS;IAC9C;IACA,IAAKO,QAAQ,IAAIC,QAAQ,IAAM,CAACD,QAAQ,IAAI,CAACC,QAAS,EAAE;MACtD,OAAO,CAAC;IACV;IACA,OAAOD,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;EAC1B,CAAC,CAAC;EAEJ,IAAI,CAACN,YAAY,CAAC9B,MAAM,EAAE;IACxB,MAAM,KAAID,yBAAe,EACvB,gEAAgEP,WAAW,IAC7E,CAAC;EACH;EACA,OAAOsC,YAAY,CAACN,GAAG,CAAEc,KAAK,IAAK1E,IAAI,CAAD,CAAC,CAAC2E,IAAI,CAAC/C,WAAW,EAAE8C,KAAK,CAAC,CAAC;AACnE;;AAEA;AACA;AACA;AACO,SAASE,mBAAmBA,CAAChD,WAAmB,EAAU;EAC/D,MAAM,CAACC,KAAK,EAAE,GAAGC,KAAK,CAAC,GAAGkC,uBAAuB,CAACpC,WAAW,CAAC;EAE9D,IAAIE,KAAK,CAACM,MAAM,EAAE;IAChBC,iBAAiB,CAAC;MAChBC,GAAG,EAAE,WAAW;MAChBC,QAAQ,EAAE,aAAa;MACvBX,WAAW;MACXC,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOD,KAAK;AACd;AAEO,SAASgD,qBAAqBA,CAACjD,WAAmB,EAAY;EACnE,MAAMkD,YAAY,GAAGd,uBAAuB,CAACpC,WAAW,CAAC;EACzD,MAAMmD,KAAK,GAAGD,YAAY,CACvBlB,GAAG,CAAEc,KAAK,IAAK1E,IAAI,CAAD,CAAC,CAAC2E,IAAI,CAACD,KAAK,EAAE,iBAAiB,CAAC,CAAC,CACnDP,MAAM,CAAEO,KAAK,IAAK,IAAAM,gBAAU,EAACN,KAAK,CAAC,CAAC;EAEvC,IAAI,CAACK,KAAK,CAAC3C,MAAM,EAAE;IACjB,MAAM,KAAID,yBAAe,EACvB,gFAAgFP,WAAW,IAC7F,CAAC;EACH;EACA,OAAOmD,KAAK;AACd;AAEO,SAASE,iBAAiBA,CAACrD,WAAmB,EAAU;EAC7D,MAAM,CAACC,KAAK,EAAE,GAAGC,KAAK,CAAC,GAAG+C,qBAAqB,CAACjD,WAAW,CAAC;EAE5D,IAAIE,KAAK,CAACM,MAAM,EAAE;IAChBC,iBAAiB,CAAC;MAChBC,GAAG,EAAE,iBAAiB;MACtBC,QAAQ,EAAE,iBAAiB;MAC3BX,WAAW;MACXC,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOD,KAAK;AACd;AAEO,SAASqD,oBAAoBA,CAACtD,WAAmB,EAAY;EAClE,MAAMmD,KAAK,GAAG,IAAAhD,YAAQ,EAAC,kBAAkB,EAAE;IACzCC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAEL,WAAW;IAChBM,MAAM,EAAER;EACV,CAAC,CAAC,CAAC4C,IAAI;EACL;EACA,CAACtD,CAAC,EAAEuD,CAAC,KAAKvD,CAAC,CAACoB,MAAM,GAAGmC,CAAC,CAACnC,MACzB,CAAC;EAED,IAAI,CAAC2C,KAAK,CAAC3C,MAAM,EAAE;IACjB,MAAM,KAAID,yBAAe,EACvB,uDAAuDP,WAAW,IACpE,CAAC;EACH;EACA,OAAOmD,KAAK;AACd;AAEO,SAASI,gBAAgBA,CAACvD,WAAmB,EAAU;EAC5D,MAAM,CAACC,KAAK,EAAE,GAAGC,KAAK,CAAC,GAAGoD,oBAAoB,CAACtD,WAAW,CAAC;EAE3D,IAAIE,KAAK,CAACM,MAAM,EAAE;IAChBC,iBAAiB,CAAC;MAChBC,GAAG,EAAE,YAAY;MACjBC,QAAQ,EAAE,YAAY;MACtBX,WAAW;MACXC,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOD,KAAK;AACd;AAEO,SAASuD,uBAAuBA,CAACxD,WAAmB,EAAY;EACrE,MAAMmD,KAAK,GAAG,IAAAhD,YAAQ,EAAC,sBAAsB,EAAE;IAC7CC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAEL,WAAW;IAChBM,MAAM,EAAER;EACV,CAAC,CAAC;EACF,OAAOqD,KAAK;AACd;;AAEA;AACA;AACA;AACO,SAASM,mBAAmBA,CAACzD,WAAmB,EAAiB;EACtE,OAAO1B,YAAY,CAAD,CAAC,CAACmF,mBAAmB,CAACzD,WAAW,CAAC;AACtD;AAEO,SAAS0D,iBAAiBA,CAAC1D,WAAmB,EAAU;EAC7D,OAAO5B,IAAI,CAAD,CAAC,CAACuF,OAAO,CAAC3D,WAAW,EAAE,KAAK,EAAE5B,IAAI,CAAD,CAAC,CAAC+C,QAAQ,CAACO,aAAa,CAAC1B,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC;AAClG;AAEO,SAAS4D,gBAAgBA,CAAC5D,WAAmB,EAAU;EAC5D,MAAM6D,cAAc,GAAGH,iBAAiB,CAAC1D,WAAW,CAAC;EACrD,OAAO5B,IAAI,CAAD,CAAC,CAAC2E,IAAI,CAACc,cAAc,EAAE,YAAY,CAAC;AAChD;AAEA,SAASpD,iBAAiBA,CAAC;EACzBC,GAAG;EACHC,QAAQ;EACRX,WAAW;EACXC,KAAK;EACLC;AAOF,CAAC,EAAE;EACD,MAAM4D,SAAS,GAAG9D,WAAW,GAAG5B,IAAI,CAAD,CAAC,CAAC2F,QAAQ,CAAC/D,WAAW,EAAEC,KAAK,CAAC,GAAGA,KAAK;EACzE,MAAM+D,UAAU,GAAGhE,WAAW,GAAGE,KAAK,CAAC8B,GAAG,CAAEiC,CAAC,IAAK7F,IAAI,CAAD,CAAC,CAAC2F,QAAQ,CAAC/D,WAAW,EAAEiE,CAAC,CAAC,CAAC,GAAG/D,KAAK;EACxF,IAAAgE,yBAAa,EACX,SAASxD,GAAG,EAAE,EACd,kBAAkBC,QAAQ,uBAAuBmD,SAAS,qBAAqBK,IAAI,CAACC,SAAS,CAC3FJ,UACF,CAAC,EACH,CAAC;AACH", "ignoreList": []}