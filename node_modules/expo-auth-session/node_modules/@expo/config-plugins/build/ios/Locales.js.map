{"version": 3, "file": "Locales.js", "names": ["_jsonFile", "data", "_interopRequireDefault", "require", "_fs", "_path", "_Xcodeproj", "_iosPlugins", "_warnings", "obj", "__esModule", "default", "withLocales", "config", "withXcodeProject", "modResults", "setLocalesAsync", "projectRoot", "modRequest", "project", "exports", "getLocales", "locales", "localesMap", "getResolvedLocalesAsync", "projectName", "getProjectName", "supportingDirectory", "join", "stringName", "lang", "localizationObj", "Object", "entries", "dir", "fs", "promises", "mkdir", "recursive", "strings", "buffer", "plist<PERSON><PERSON>", "localVersion", "push", "writeFile", "groupName", "group", "ensureGroupRecursively", "children", "some", "comment", "addResourceFileToGroup", "filepath", "relative", "isBuildFile", "verbose", "input", "localeJsonPath", "JsonFile", "readAsync", "addWarningIOS"], "sources": ["../../src/ios/Locales.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport JsonFile from '@expo/json-file';\nimport fs from 'fs';\nimport { join, relative } from 'path';\nimport { XcodeProject } from 'xcode';\n\nimport { addResourceFileToGroup, ensureGroupRecursively, getProjectName } from './utils/Xcodeproj';\nimport { ConfigPlugin } from '../Plugin.types';\nimport { withXcodeProject } from '../plugins/ios-plugins';\nimport { addWarningIOS } from '../utils/warnings';\n\ntype LocaleJson = Record<string, string>;\ntype ResolvedLocalesJson = Record<string, LocaleJson>;\ntype ExpoConfigLocales = NonNullable<ExpoConfig['locales']>;\n\nexport const withLocales: ConfigPlugin = (config) => {\n  return withXcodeProject(config, async (config) => {\n    config.modResults = await setLocalesAsync(config, {\n      projectRoot: config.modRequest.projectRoot,\n      project: config.modResults,\n    });\n    return config;\n  });\n};\n\nexport function getLocales(\n  config: Pick<ExpoConfig, 'locales'>\n): Record<string, string | LocaleJson> | null {\n  return config.locales ?? null;\n}\n\nexport async function setLocalesAsync(\n  config: Pick<ExpoConfig, 'locales'>,\n  { projectRoot, project }: { projectRoot: string; project: XcodeProject }\n): Promise<XcodeProject> {\n  const locales = getLocales(config);\n  if (!locales) {\n    return project;\n  }\n  // possibly validate CFBundleAllowMixedLocalizations is enabled\n  const localesMap = await getResolvedLocalesAsync(projectRoot, locales);\n\n  const projectName = getProjectName(projectRoot);\n  const supportingDirectory = join(projectRoot, 'ios', projectName, 'Supporting');\n\n  // TODO: Should we delete all before running? Revisit after we land on a lock file.\n  const stringName = 'InfoPlist.strings';\n\n  for (const [lang, localizationObj] of Object.entries(localesMap)) {\n    const dir = join(supportingDirectory, `${lang}.lproj`);\n    // await fs.ensureDir(dir);\n    await fs.promises.mkdir(dir, { recursive: true });\n\n    const strings = join(dir, stringName);\n    const buffer = [];\n    for (const [plistKey, localVersion] of Object.entries(localizationObj)) {\n      buffer.push(`${plistKey} = \"${localVersion}\";`);\n    }\n    // Write the file to the file system.\n    await fs.promises.writeFile(strings, buffer.join('\\n'));\n\n    const groupName = `${projectName}/Supporting/${lang}.lproj`;\n    // deep find the correct folder\n    const group = ensureGroupRecursively(project, groupName);\n\n    // Ensure the file doesn't already exist\n    if (!group?.children.some(({ comment }) => comment === stringName)) {\n      // Only write the file if it doesn't already exist.\n      project = addResourceFileToGroup({\n        filepath: relative(supportingDirectory, strings),\n        groupName,\n        project,\n        isBuildFile: true,\n        verbose: true,\n      });\n    }\n  }\n\n  return project;\n}\n\nexport async function getResolvedLocalesAsync(\n  projectRoot: string,\n  input: ExpoConfigLocales\n): Promise<ResolvedLocalesJson> {\n  const locales: ResolvedLocalesJson = {};\n  for (const [lang, localeJsonPath] of Object.entries(input)) {\n    if (typeof localeJsonPath === 'string') {\n      try {\n        locales[lang] = await JsonFile.readAsync(join(projectRoot, localeJsonPath));\n      } catch {\n        // Add a warning when a json file cannot be parsed.\n        addWarningIOS(\n          `locales.${lang}`,\n          `Failed to parse JSON of locale file for language: ${lang}`,\n          'https://docs.expo.dev/distribution/app-stores/#localizing-your-ios-app'\n        );\n      }\n    } else {\n      // In the off chance that someone defined the locales json in the config, pass it directly to the object.\n      // We do this to make the types more elegant.\n      locales[lang] = localeJsonPath;\n    }\n  }\n\n  return locales;\n}\n"], "mappings": ";;;;;;;;;AACA,SAAAA,UAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,SAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,IAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,GAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,MAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,KAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAK,WAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,UAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAM,YAAA;EAAA,MAAAN,IAAA,GAAAE,OAAA;EAAAI,WAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,UAAA;EAAA,MAAAP,IAAA,GAAAE,OAAA;EAAAK,SAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAkD,SAAAC,uBAAAO,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAM3C,MAAMG,WAAyB,GAAIC,MAAM,IAAK;EACnD,OAAO,IAAAC,8BAAgB,EAACD,MAAM,EAAE,MAAOA,MAAM,IAAK;IAChDA,MAAM,CAACE,UAAU,GAAG,MAAMC,eAAe,CAACH,MAAM,EAAE;MAChDI,WAAW,EAAEJ,MAAM,CAACK,UAAU,CAACD,WAAW;MAC1CE,OAAO,EAAEN,MAAM,CAACE;IAClB,CAAC,CAAC;IACF,OAAOF,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACO,OAAA,CAAAR,WAAA,GAAAA,WAAA;AAEK,SAASS,UAAUA,CACxBR,MAAmC,EACS;EAC5C,OAAOA,MAAM,CAACS,OAAO,IAAI,IAAI;AAC/B;AAEO,eAAeN,eAAeA,CACnCH,MAAmC,EACnC;EAAEI,WAAW;EAAEE;AAAwD,CAAC,EACjD;EACvB,MAAMG,OAAO,GAAGD,UAAU,CAACR,MAAM,CAAC;EAClC,IAAI,CAACS,OAAO,EAAE;IACZ,OAAOH,OAAO;EAChB;EACA;EACA,MAAMI,UAAU,GAAG,MAAMC,uBAAuB,CAACP,WAAW,EAAEK,OAAO,CAAC;EAEtE,MAAMG,WAAW,GAAG,IAAAC,2BAAc,EAACT,WAAW,CAAC;EAC/C,MAAMU,mBAAmB,GAAG,IAAAC,YAAI,EAACX,WAAW,EAAE,KAAK,EAAEQ,WAAW,EAAE,YAAY,CAAC;;EAE/E;EACA,MAAMI,UAAU,GAAG,mBAAmB;EAEtC,KAAK,MAAM,CAACC,IAAI,EAAEC,eAAe,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACV,UAAU,CAAC,EAAE;IAChE,MAAMW,GAAG,GAAG,IAAAN,YAAI,EAACD,mBAAmB,EAAE,GAAGG,IAAI,QAAQ,CAAC;IACtD;IACA,MAAMK,aAAE,CAACC,QAAQ,CAACC,KAAK,CAACH,GAAG,EAAE;MAAEI,SAAS,EAAE;IAAK,CAAC,CAAC;IAEjD,MAAMC,OAAO,GAAG,IAAAX,YAAI,EAACM,GAAG,EAAEL,UAAU,CAAC;IACrC,MAAMW,MAAM,GAAG,EAAE;IACjB,KAAK,MAAM,CAACC,QAAQ,EAAEC,YAAY,CAAC,IAAIV,MAAM,CAACC,OAAO,CAACF,eAAe,CAAC,EAAE;MACtES,MAAM,CAACG,IAAI,CAAC,GAAGF,QAAQ,OAAOC,YAAY,IAAI,CAAC;IACjD;IACA;IACA,MAAMP,aAAE,CAACC,QAAQ,CAACQ,SAAS,CAACL,OAAO,EAAEC,MAAM,CAACZ,IAAI,CAAC,IAAI,CAAC,CAAC;IAEvD,MAAMiB,SAAS,GAAG,GAAGpB,WAAW,eAAeK,IAAI,QAAQ;IAC3D;IACA,MAAMgB,KAAK,GAAG,IAAAC,mCAAsB,EAAC5B,OAAO,EAAE0B,SAAS,CAAC;;IAExD;IACA,IAAI,CAACC,KAAK,EAAEE,QAAQ,CAACC,IAAI,CAAC,CAAC;MAAEC;IAAQ,CAAC,KAAKA,OAAO,KAAKrB,UAAU,CAAC,EAAE;MAClE;MACAV,OAAO,GAAG,IAAAgC,mCAAsB,EAAC;QAC/BC,QAAQ,EAAE,IAAAC,gBAAQ,EAAC1B,mBAAmB,EAAEY,OAAO,CAAC;QAChDM,SAAS;QACT1B,OAAO;QACPmC,WAAW,EAAE,IAAI;QACjBC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF;EAEA,OAAOpC,OAAO;AAChB;AAEO,eAAeK,uBAAuBA,CAC3CP,WAAmB,EACnBuC,KAAwB,EACM;EAC9B,MAAMlC,OAA4B,GAAG,CAAC,CAAC;EACvC,KAAK,MAAM,CAACQ,IAAI,EAAE2B,cAAc,CAAC,IAAIzB,MAAM,CAACC,OAAO,CAACuB,KAAK,CAAC,EAAE;IAC1D,IAAI,OAAOC,cAAc,KAAK,QAAQ,EAAE;MACtC,IAAI;QACFnC,OAAO,CAACQ,IAAI,CAAC,GAAG,MAAM4B,mBAAQ,CAACC,SAAS,CAAC,IAAA/B,YAAI,EAACX,WAAW,EAAEwC,cAAc,CAAC,CAAC;MAC7E,CAAC,CAAC,MAAM;QACN;QACA,IAAAG,yBAAa,EACX,WAAW9B,IAAI,EAAE,EACjB,qDAAqDA,IAAI,EAAE,EAC3D,wEACF,CAAC;MACH;IACF,CAAC,MAAM;MACL;MACA;MACAR,OAAO,CAACQ,IAAI,CAAC,GAAG2B,cAAc;IAChC;EACF;EAEA,OAAOnC,OAAO;AAChB", "ignoreList": []}