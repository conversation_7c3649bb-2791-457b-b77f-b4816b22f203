{"version": 3, "file": "withStaticPlugin.js", "names": ["_assert", "data", "_interopRequireDefault", "require", "_getenv", "_errors", "_pluginResolver", "obj", "__esModule", "default", "EXPO_DEBUG", "boolish", "EXPO_CONFIG_PLUGIN_VERBOSE_ERRORS", "EXPO_USE_UNVERSIONED_PLUGINS", "isModuleMissingError", "name", "error", "includes", "code", "message", "isUnexpectedTokenError", "SyntaxError", "PluginError", "match", "withStaticPlugin", "config", "props", "projectRoot", "_internal", "assertInternalProjectRoot", "pluginResolve", "pluginProps", "normalizeStaticPlugin", "plugin", "assert", "_resolverError", "with<PERSON><PERSON><PERSON>", "resolveConfigPluginFunction", "_isLegacyPlugin", "fallback", "console", "log", "shouldMuteWarning", "exports"], "sources": ["../../src/plugins/withStaticPlugin.ts"], "sourcesContent": ["import assert from 'assert';\nimport { boolish } from 'getenv';\n\nimport { ConfigPlugin, StaticPlugin } from '../Plugin.types';\nimport { PluginError } from '../utils/errors';\nimport {\n  assertInternalProjectRoot,\n  normalizeStaticPlugin,\n  resolveConfigPluginFunction,\n} from '../utils/plugin-resolver';\n\nconst EXPO_DEBUG = boolish('EXPO_DEBUG', false);\n\n// Show all error info related to plugin resolution.\nconst EXPO_CONFIG_PLUGIN_VERBOSE_ERRORS = boolish('EXPO_CONFIG_PLUGIN_VERBOSE_ERRORS', false);\n// Force using the fallback unversioned plugin instead of a local versioned copy,\n// this should only be used for testing the CLI.\nconst EXPO_USE_UNVERSIONED_PLUGINS = boolish('EXPO_USE_UNVERSIONED_PLUGINS', false);\n\nfunction isModuleMissingError(name: string, error: Error): boolean {\n  // @ts-ignore\n  if (['MODULE_NOT_FOUND', 'PLU<PERSON>N_NOT_FOUND'].includes(error.code)) {\n    return true;\n  }\n  return error.message.includes(`Cannot find module '${name}'`);\n}\n\nfunction isUnexpectedTokenError(error: Error): boolean {\n  if (\n    error instanceof SyntaxError ||\n    (error instanceof PluginError && error.code === 'INVALID_PLUGIN_IMPORT')\n  ) {\n    return (\n      // These are the most common errors that'll be thrown when a package isn't transpiled correctly.\n      !!error.message.match(/Unexpected token/) ||\n      !!error.message.match(/Cannot use import statement/)\n    );\n  }\n  return false;\n}\n\n/**\n * Resolves static module plugin and potentially falls back on a provided plugin if the module cannot be resolved\n *\n * @param config\n * @param fallback Plugin with `_resolverError` explaining why the module couldn't be used\n * @param projectRoot optional project root, fallback to _internal.projectRoot. Used for testing.\n * @param _isLegacyPlugin Used to suppress errors thrown by plugins that are applied automatically\n */\nexport const withStaticPlugin: ConfigPlugin<{\n  plugin: StaticPlugin | ConfigPlugin | string;\n  fallback?: ConfigPlugin<{ _resolverError: Error } & any>;\n  projectRoot?: string;\n  _isLegacyPlugin?: boolean;\n}> = (config, props) => {\n  let projectRoot = props.projectRoot;\n  if (!projectRoot) {\n    projectRoot = config._internal?.projectRoot;\n    assertInternalProjectRoot(projectRoot);\n  }\n\n  let [pluginResolve, pluginProps] = normalizeStaticPlugin(props.plugin);\n  // Ensure no one uses this property by accident.\n  assert(\n    !pluginProps?._resolverError,\n    `Plugin property '_resolverError' is a reserved property of \\`withStaticPlugin\\``\n  );\n\n  let withPlugin: ConfigPlugin<unknown>;\n\n  if (\n    // Function was provided, no need to resolve: [withPlugin, {}]\n    typeof pluginResolve === 'function'\n  ) {\n    withPlugin = pluginResolve;\n  } else if (typeof pluginResolve === 'string') {\n    try {\n      // Resolve and evaluate plugins.\n      withPlugin = resolveConfigPluginFunction(projectRoot, pluginResolve);\n\n      // Only force if the project has the versioned plugin, otherwise use default behavior.\n      // This helps see which plugins are being skipped.\n      if (\n        EXPO_USE_UNVERSIONED_PLUGINS &&\n        !!withPlugin &&\n        !!props._isLegacyPlugin &&\n        !!props.fallback\n      ) {\n        console.log(`Force \"${pluginResolve}\" to unversioned plugin`);\n        withPlugin = props.fallback;\n      }\n    } catch (error: any) {\n      if (EXPO_DEBUG) {\n        if (EXPO_CONFIG_PLUGIN_VERBOSE_ERRORS) {\n          // Log the error in debug mode for plugins with fallbacks (like the Expo managed plugins).\n          console.log(`Error resolving plugin \"${pluginResolve}\"`);\n          console.log(error);\n          console.log();\n        } else {\n          const shouldMuteWarning =\n            props._isLegacyPlugin &&\n            (isModuleMissingError(pluginResolve, error) || isUnexpectedTokenError(error));\n          if (!shouldMuteWarning) {\n            if (isModuleMissingError(pluginResolve, error)) {\n              // Prevent causing log spew for basic resolution errors.\n              console.log(`Could not find plugin \"${pluginResolve}\"`);\n            } else {\n              // Log the error in debug mode for plugins with fallbacks (like the Expo managed plugins).\n              console.log(`Error resolving plugin \"${pluginResolve}\"`);\n              console.log(error);\n              console.log();\n            }\n          }\n        }\n      }\n      // TODO: Maybe allow for `PluginError`s to be thrown so external plugins can assert invalid options.\n\n      // If the static module failed to resolve, attempt to use a fallback.\n      // This enables support for built-in plugins with versioned variations living in other packages.\n      if (props.fallback) {\n        if (!pluginProps) pluginProps = {};\n        // Pass this to the fallback plugin for potential warnings about needing to install a versioned package.\n        pluginProps._resolverError = error;\n        withPlugin = props.fallback;\n      } else {\n        // If no fallback, throw the resolution error.\n        throw error;\n      }\n    }\n  } else {\n    throw new PluginError(\n      `Plugin is an unexpected type: ${typeof pluginResolve}`,\n      'INVALID_PLUGIN_TYPE'\n    );\n  }\n\n  // Execute the plugin.\n  config = withPlugin(config, pluginProps);\n  return config;\n};\n"], "mappings": ";;;;;;AAAA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,QAAA;EAAA,MAAAH,IAAA,GAAAE,OAAA;EAAAC,OAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAI,QAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,OAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,gBAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,eAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAIkC,SAAAC,uBAAAK,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAElC,MAAMG,UAAU,GAAG,IAAAC,iBAAO,EAAC,YAAY,EAAE,KAAK,CAAC;;AAE/C;AACA,MAAMC,iCAAiC,GAAG,IAAAD,iBAAO,EAAC,mCAAmC,EAAE,KAAK,CAAC;AAC7F;AACA;AACA,MAAME,4BAA4B,GAAG,IAAAF,iBAAO,EAAC,8BAA8B,EAAE,KAAK,CAAC;AAEnF,SAASG,oBAAoBA,CAACC,IAAY,EAAEC,KAAY,EAAW;EACjE;EACA,IAAI,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,CAACC,QAAQ,CAACD,KAAK,CAACE,IAAI,CAAC,EAAE;IACjE,OAAO,IAAI;EACb;EACA,OAAOF,KAAK,CAACG,OAAO,CAACF,QAAQ,CAAC,uBAAuBF,IAAI,GAAG,CAAC;AAC/D;AAEA,SAASK,sBAAsBA,CAACJ,KAAY,EAAW;EACrD,IACEA,KAAK,YAAYK,WAAW,IAC3BL,KAAK,YAAYM,qBAAW,IAAIN,KAAK,CAACE,IAAI,KAAK,uBAAwB,EACxE;IACA;MACE;MACA,CAAC,CAACF,KAAK,CAACG,OAAO,CAACI,KAAK,CAAC,kBAAkB,CAAC,IACzC,CAAC,CAACP,KAAK,CAACG,OAAO,CAACI,KAAK,CAAC,6BAA6B;IAAC;EAExD;EACA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMC,gBAKX,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EACtB,IAAIC,WAAW,GAAGD,KAAK,CAACC,WAAW;EACnC,IAAI,CAACA,WAAW,EAAE;IAChBA,WAAW,GAAGF,MAAM,CAACG,SAAS,EAAED,WAAW;IAC3C,IAAAE,2CAAyB,EAACF,WAAW,CAAC;EACxC;EAEA,IAAI,CAACG,aAAa,EAAEC,WAAW,CAAC,GAAG,IAAAC,uCAAqB,EAACN,KAAK,CAACO,MAAM,CAAC;EACtE;EACA,IAAAC,iBAAM,EACJ,CAACH,WAAW,EAAEI,cAAc,EAC5B,iFACF,CAAC;EAED,IAAIC,UAAiC;EAErC;EACE;EACA,OAAON,aAAa,KAAK,UAAU,EACnC;IACAM,UAAU,GAAGN,aAAa;EAC5B,CAAC,MAAM,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;IAC5C,IAAI;MACF;MACAM,UAAU,GAAG,IAAAC,6CAA2B,EAACV,WAAW,EAAEG,aAAa,CAAC;;MAEpE;MACA;MACA,IACEjB,4BAA4B,IAC5B,CAAC,CAACuB,UAAU,IACZ,CAAC,CAACV,KAAK,CAACY,eAAe,IACvB,CAAC,CAACZ,KAAK,CAACa,QAAQ,EAChB;QACAC,OAAO,CAACC,GAAG,CAAC,UAAUX,aAAa,yBAAyB,CAAC;QAC7DM,UAAU,GAAGV,KAAK,CAACa,QAAQ;MAC7B;IACF,CAAC,CAAC,OAAOvB,KAAU,EAAE;MACnB,IAAIN,UAAU,EAAE;QACd,IAAIE,iCAAiC,EAAE;UACrC;UACA4B,OAAO,CAACC,GAAG,CAAC,2BAA2BX,aAAa,GAAG,CAAC;UACxDU,OAAO,CAACC,GAAG,CAACzB,KAAK,CAAC;UAClBwB,OAAO,CAACC,GAAG,CAAC,CAAC;QACf,CAAC,MAAM;UACL,MAAMC,iBAAiB,GACrBhB,KAAK,CAACY,eAAe,KACpBxB,oBAAoB,CAACgB,aAAa,EAAEd,KAAK,CAAC,IAAII,sBAAsB,CAACJ,KAAK,CAAC,CAAC;UAC/E,IAAI,CAAC0B,iBAAiB,EAAE;YACtB,IAAI5B,oBAAoB,CAACgB,aAAa,EAAEd,KAAK,CAAC,EAAE;cAC9C;cACAwB,OAAO,CAACC,GAAG,CAAC,0BAA0BX,aAAa,GAAG,CAAC;YACzD,CAAC,MAAM;cACL;cACAU,OAAO,CAACC,GAAG,CAAC,2BAA2BX,aAAa,GAAG,CAAC;cACxDU,OAAO,CAACC,GAAG,CAACzB,KAAK,CAAC;cAClBwB,OAAO,CAACC,GAAG,CAAC,CAAC;YACf;UACF;QACF;MACF;MACA;;MAEA;MACA;MACA,IAAIf,KAAK,CAACa,QAAQ,EAAE;QAClB,IAAI,CAACR,WAAW,EAAEA,WAAW,GAAG,CAAC,CAAC;QAClC;QACAA,WAAW,CAACI,cAAc,GAAGnB,KAAK;QAClCoB,UAAU,GAAGV,KAAK,CAACa,QAAQ;MAC7B,CAAC,MAAM;QACL;QACA,MAAMvB,KAAK;MACb;IACF;EACF,CAAC,MAAM;IACL,MAAM,KAAIM,qBAAW,EACnB,iCAAiC,OAAOQ,aAAa,EAAE,EACvD,qBACF,CAAC;EACH;;EAEA;EACAL,MAAM,GAAGW,UAAU,CAACX,MAAM,EAAEM,WAAW,CAAC;EACxC,OAAON,MAAM;AACf,CAAC;AAACkB,OAAA,CAAAnB,gBAAA,GAAAA,gBAAA", "ignoreList": []}