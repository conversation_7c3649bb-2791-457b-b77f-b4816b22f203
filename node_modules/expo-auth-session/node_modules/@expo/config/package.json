{"name": "@expo/config", "version": "9.0.4", "description": "A library for interacting with the app.json", "main": "build/index.js", "scripts": {"build": "tsc --emitDeclarationOnly && babel src --out-dir build --extensions \".ts\" --source-maps --ignore \"src/**/__mocks__/*\",\"src/**/__tests__/*\"", "clean": "expo-module clean", "lint": "expo-module lint", "prepare": "expo-module clean && yarn run build", "prepublishOnly": "expo-module prepublishOnly", "test": "expo-module test", "typecheck": "expo-module typecheck"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/@expo/config"}, "keywords": ["json", "react-native", "expo", "react"], "license": "MIT", "bugs": {"url": "https://github.com/expo/expo/issues"}, "homepage": "https://github.com/expo/expo/tree/main/packages/@expo/config#readme", "files": ["build", "paths"], "dependencies": {"@babel/code-frame": "~7.10.4", "@expo/config-plugins": "~8.0.8", "@expo/config-types": "^51.0.3", "@expo/json-file": "^8.3.0", "getenv": "^1.0.0", "glob": "7.1.6", "require-from-string": "^2.0.2", "resolve-from": "^5.0.0", "semver": "^7.6.0", "slugify": "^1.3.4", "sucrase": "3.34.0"}, "devDependencies": {"@types/require-from-string": "^1.2.1", "expo-module-scripts": "^3.3.0"}, "publishConfig": {"access": "public"}, "gitHead": "babb6c584ab401bf9b75574aebd4ead1a423ec81"}