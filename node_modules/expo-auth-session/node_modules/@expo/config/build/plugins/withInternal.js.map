{"version": 3, "file": "withInternal.js", "names": ["_getenv", "data", "require", "EXPO_DEBUG", "exports", "boolish", "withInternal", "config", "internals", "_internal", "isDebug"], "sources": ["../../src/plugins/withInternal.ts"], "sourcesContent": ["import { ConfigPlugin } from '@expo/config-plugins';\nimport { boolish } from 'getenv';\n\nimport { ConfigFilePaths } from '../Config.types';\n\nexport const EXPO_DEBUG = boolish('EXPO_DEBUG', false);\n\n/**\n * Adds the _internal object.\n *\n * @param config\n * @param projectRoot\n */\nexport const withInternal: ConfigPlugin<\n  { projectRoot: string; packageJsonPath?: string } & Partial<ConfigFilePaths>\n> = (config, internals) => {\n  if (!config._internal) {\n    config._internal = {};\n  }\n\n  config._internal = {\n    isDebug: EXPO_DEBUG,\n    ...config._internal,\n    ...internals,\n  };\n\n  return config;\n};\n"], "mappings": ";;;;;;AACA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAIO,MAAME,UAAU,GAAAC,OAAA,CAAAD,UAAA,GAAG,IAAAE,iBAAO,EAAC,YAAY,EAAE,KAAK,CAAC;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACO,MAAMC,YAEZ,GAAGA,CAACC,MAAM,EAAEC,SAAS,KAAK;EACzB,IAAI,CAACD,MAAM,CAACE,SAAS,EAAE;IACrBF,MAAM,CAACE,SAAS,GAAG,CAAC,CAAC;EACvB;EAEAF,MAAM,CAACE,SAAS,GAAG;IACjBC,OAAO,EAAEP,UAAU;IACnB,GAAGI,MAAM,CAACE,SAAS;IACnB,GAAGD;EACL,CAAC;EAED,OAAOD,MAAM;AACf,CAAC;AAACH,OAAA,CAAAE,YAAA,GAAAA,YAAA", "ignoreList": []}