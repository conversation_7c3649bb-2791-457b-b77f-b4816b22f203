{"version": 3, "file": "Config.js", "names": ["_jsonFile", "data", "_interopRequireDefault", "require", "_fs", "_glob", "_path", "_resolveFrom", "_semver", "_slugify", "_getConfig", "_getExpoSDKVersion", "_withConfigPlugins", "_withInternal", "_resolvePackageJson", "_Config", "Object", "keys", "for<PERSON>ach", "key", "prototype", "hasOwnProperty", "call", "_exportNames", "exports", "defineProperty", "enumerable", "get", "obj", "__esModule", "default", "hasWarnedAboutRootConfig", "reduceExpoObject", "config", "undefined", "expo", "filter", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "str", "<PERSON><PERSON><PERSON><PERSON>", "ansi<PERSON>old", "plural", "console", "warn", "map", "join", "mods", "getSupportedPlatforms", "projectRoot", "platforms", "resolveFrom", "silent", "push", "getConfig", "options", "paths", "getConfigFilePaths", "rawStaticConfig", "staticConfigPath", "getStaticConfig", "rootConfig", "staticConfig", "packageJson", "packageJsonPath", "getPackageJsonAndPath", "fillAndReturnConfig", "dynamicConfigObjectType", "mayHaveUnusedStaticConfig", "configWithDefaultValues", "ensureConfigHasDefaultValues", "exp", "pkg", "skipSDKVersionRequirement", "dynamicConfigPath", "hasUnusedStaticConfig", "isModdedConfig", "withConfigPlugins", "skip<PERSON>lug<PERSON>", "isPublicConfig", "_internal", "hooks", "ios", "android", "updates", "codeSigningCertificate", "codeSigningMetadata", "getContextConfig", "exportedObjectType", "rawDynamicConfig", "getDynamicConfig", "dynamicConfig", "getPackageJson", "getRootPackageJsonPath", "JsonFile", "read", "getDynamicConfigFilePath", "getStaticConfigFilePath", "fileName", "config<PERSON><PERSON>", "path", "fs", "existsSync", "modifyConfigAsync", "modifications", "readOptions", "writeOptions", "type", "message", "relative", "outputConfig", "dryRun", "writeAsync", "json5", "withInternal", "pkgName", "name", "basename", "pkgVersion", "version", "pkgWithDefaults", "slug", "slugify", "toLowerCase", "description", "expWithDefaults", "sdkVersion", "getExpoSDKVersion", "error", "DEFAULT_BUILD_PATH", "getWebOutputPath", "process", "env", "WEBPACK_BUILD_OUTPUT_PATH", "web", "build", "output", "getNameFromConfig", "appManifest", "appName", "displayName", "webName", "getDefaultTarget", "semver", "lt", "isBareWorkflowProject", "dependencies", "expokit", "xcodeprojFiles", "globSync", "absolute", "cwd", "gradleFiles", "getProjectConfigDescription", "getProjectConfigDescriptionWithPaths", "projectConfig", "relativeDynamicConfigPath"], "sources": ["../src/Config.ts"], "sourcesContent": ["import { ModConfig } from '@expo/config-plugins';\nimport Json<PERSON><PERSON>, { JSONObject } from '@expo/json-file';\nimport fs from 'fs';\nimport { sync as globSync } from 'glob';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\nimport semver from 'semver';\nimport slugify from 'slugify';\n\nimport {\n  AppJSONConfig,\n  ConfigFilePaths,\n  ExpoConfig,\n  GetConfigOptions,\n  PackageJSONConfig,\n  Platform,\n  ProjectConfig,\n  ProjectTarget,\n  WriteConfigOptions,\n} from './Config.types';\nimport { getDynamicConfig, getStaticConfig } from './getConfig';\nimport { getExpoSDKVersion } from './getExpoSDKVersion';\nimport { withConfigPlugins } from './plugins/withConfigPlugins';\nimport { withInternal } from './plugins/withInternal';\nimport { getRootPackageJsonPath } from './resolvePackageJson';\n\ntype SplitConfigs = { expo: ExpoConfig; mods: ModConfig };\n\nlet hasWarnedAboutRootConfig = false;\n\n/**\n * If a config has an `expo` object then that will be used as the config.\n * This method reduces out other top level values if an `expo` object exists.\n *\n * @param config Input config object to reduce\n */\nfunction reduceExpoObject(config?: any): SplitConfigs {\n  if (!config) return config === undefined ? null : config;\n\n  if (config.expo && !hasWarnedAboutRootConfig) {\n    const keys = Object.keys(config).filter((key) => key !== 'expo');\n    if (keys.length) {\n      hasWarnedAboutRootConfig = true;\n      const ansiYellow = (str: string) => `\\u001B[33m${str}\\u001B[0m`;\n      const ansiGray = (str: string) => `\\u001B[90m${str}\\u001B[0m`;\n      const ansiBold = (str: string) => `\\u001B[1m${str}\\u001B[22m`;\n      const plural = keys.length > 1;\n      console.warn(\n        ansiYellow(\n          ansiBold('Warning: ') +\n            `Root-level ${ansiBold(`\"expo\"`)} object found. Ignoring extra key${plural ? 's' : ''} in Expo config: ${keys\n              .map((key) => `\"${key}\"`)\n              .join(', ')}\\n` +\n            ansiGray(`Learn more: https://expo.fyi/root-expo-object`)\n        )\n      );\n    }\n  }\n\n  const { mods, ...expo } = config.expo ?? config;\n\n  return {\n    expo,\n    mods,\n  };\n}\n\n/**\n * Get all platforms that a project is currently capable of running.\n *\n * @param projectRoot\n * @param exp\n */\nfunction getSupportedPlatforms(projectRoot: string): Platform[] {\n  const platforms: Platform[] = [];\n  if (resolveFrom.silent(projectRoot, 'react-native')) {\n    platforms.push('ios', 'android');\n  }\n  if (resolveFrom.silent(projectRoot, 'react-native-web')) {\n    platforms.push('web');\n  }\n  return platforms;\n}\n\n/**\n * Evaluate the config for an Expo project.\n * If a function is exported from the `app.config.js` then a partial config will be passed as an argument.\n * The partial config is composed from any existing app.json, and certain fields from the `package.json` like name and description.\n *\n * If options.isPublicConfig is true, the Expo config will include only public-facing options (omitting private keys).\n * The resulting config should be suitable for hosting or embedding in a publicly readable location.\n *\n * **Example**\n * ```js\n * module.exports = function({ config }) {\n *   // mutate the config before returning it.\n *   config.slug = 'new slug'\n *   return { expo: config };\n * }\n * ```\n *\n * **Supports**\n * - `app.config.ts`\n * - `app.config.js`\n * - `app.config.json`\n * - `app.json`\n *\n * @param projectRoot the root folder containing all of your application code\n * @param options enforce criteria for a project config\n */\nexport function getConfig(projectRoot: string, options: GetConfigOptions = {}): ProjectConfig {\n  const paths = getConfigFilePaths(projectRoot);\n\n  const rawStaticConfig = paths.staticConfigPath ? getStaticConfig(paths.staticConfigPath) : null;\n  // For legacy reasons, always return an object.\n  const rootConfig = (rawStaticConfig || {}) as AppJSONConfig;\n  const staticConfig = reduceExpoObject(rawStaticConfig) || {};\n\n  // Can only change the package.json location if an app.json or app.config.json exists\n  const [packageJson, packageJsonPath] = getPackageJsonAndPath(projectRoot);\n\n  function fillAndReturnConfig(\n    config: SplitConfigs,\n    dynamicConfigObjectType: string | null,\n    mayHaveUnusedStaticConfig: boolean = false\n  ) {\n    const configWithDefaultValues = {\n      ...ensureConfigHasDefaultValues({\n        projectRoot,\n        exp: config.expo,\n        pkg: packageJson,\n        skipSDKVersionRequirement: options.skipSDKVersionRequirement,\n        paths,\n        packageJsonPath,\n      }),\n      mods: config.mods,\n      dynamicConfigObjectType,\n      rootConfig,\n      dynamicConfigPath: paths.dynamicConfigPath,\n      staticConfigPath: paths.staticConfigPath,\n      hasUnusedStaticConfig:\n        !!paths.staticConfigPath && !!paths.dynamicConfigPath && mayHaveUnusedStaticConfig,\n    };\n\n    if (options.isModdedConfig) {\n      // @ts-ignore: Add the mods back to the object.\n      configWithDefaultValues.exp.mods = config.mods ?? null;\n    }\n\n    // Apply static json plugins, should be done after _internal\n    configWithDefaultValues.exp = withConfigPlugins(\n      configWithDefaultValues.exp,\n      !!options.skipPlugins\n    );\n\n    if (!options.isModdedConfig) {\n      // @ts-ignore: Delete mods added by static plugins when they won't have a chance to be evaluated\n      delete configWithDefaultValues.exp.mods;\n    }\n\n    if (options.isPublicConfig) {\n      // TODD(EvanBacon): Drop plugins array after it's been resolved.\n\n      // Remove internal values with references to user's file paths from the public config.\n      delete configWithDefaultValues.exp._internal;\n\n      // hooks no longer exists in the typescript type but should still be removed\n      if ('hooks' in configWithDefaultValues.exp) {\n        delete configWithDefaultValues.exp.hooks;\n      }\n      if (configWithDefaultValues.exp.ios?.config) {\n        delete configWithDefaultValues.exp.ios.config;\n      }\n      if (configWithDefaultValues.exp.android?.config) {\n        delete configWithDefaultValues.exp.android.config;\n      }\n\n      delete configWithDefaultValues.exp.updates?.codeSigningCertificate;\n      delete configWithDefaultValues.exp.updates?.codeSigningMetadata;\n    }\n\n    return configWithDefaultValues;\n  }\n\n  // Fill in the static config\n  function getContextConfig(config: SplitConfigs) {\n    return ensureConfigHasDefaultValues({\n      projectRoot,\n      exp: config.expo,\n      pkg: packageJson,\n      skipSDKVersionRequirement: true,\n      paths,\n      packageJsonPath,\n    }).exp;\n  }\n\n  if (paths.dynamicConfigPath) {\n    // No app.config.json or app.json but app.config.js\n    const {\n      exportedObjectType,\n      config: rawDynamicConfig,\n      mayHaveUnusedStaticConfig,\n    } = getDynamicConfig(paths.dynamicConfigPath, {\n      projectRoot,\n      staticConfigPath: paths.staticConfigPath,\n      packageJsonPath,\n      config: getContextConfig(staticConfig),\n    });\n    // Allow for the app.config.js to `export default null;`\n    // Use `dynamicConfigPath` to detect if a dynamic config exists.\n    const dynamicConfig = reduceExpoObject(rawDynamicConfig) || {};\n    return fillAndReturnConfig(dynamicConfig, exportedObjectType, mayHaveUnusedStaticConfig);\n  }\n\n  // No app.config.js but json or no config\n  return fillAndReturnConfig(staticConfig || {}, null);\n}\n\nexport function getPackageJson(projectRoot: string): PackageJSONConfig {\n  const [pkg] = getPackageJsonAndPath(projectRoot);\n  return pkg;\n}\n\nfunction getPackageJsonAndPath(projectRoot: string): [PackageJSONConfig, string] {\n  const packageJsonPath = getRootPackageJsonPath(projectRoot);\n  return [JsonFile.read(packageJsonPath), packageJsonPath];\n}\n\n/**\n * Get the static and dynamic config paths for a project. Also accounts for custom paths.\n *\n * @param projectRoot\n */\nexport function getConfigFilePaths(projectRoot: string): ConfigFilePaths {\n  return {\n    dynamicConfigPath: getDynamicConfigFilePath(projectRoot),\n    staticConfigPath: getStaticConfigFilePath(projectRoot),\n  };\n}\n\nfunction getDynamicConfigFilePath(projectRoot: string): string | null {\n  for (const fileName of ['app.config.ts', 'app.config.js']) {\n    const configPath = path.join(projectRoot, fileName);\n    if (fs.existsSync(configPath)) {\n      return configPath;\n    }\n  }\n  return null;\n}\n\nfunction getStaticConfigFilePath(projectRoot: string): string | null {\n  for (const fileName of ['app.config.json', 'app.json']) {\n    const configPath = path.join(projectRoot, fileName);\n    if (fs.existsSync(configPath)) {\n      return configPath;\n    }\n  }\n  return null;\n}\n\n/**\n * Attempt to modify an Expo project config.\n * This will only fully work if the project is using static configs only.\n * Otherwise 'warn' | 'fail' will return with a message about why the config couldn't be updated.\n * The potentially modified config object will be returned for testing purposes.\n *\n * @param projectRoot\n * @param modifications modifications to make to an existing config\n * @param readOptions options for reading the current config file\n * @param writeOptions If true, the static config file will not be rewritten\n */\nexport async function modifyConfigAsync(\n  projectRoot: string,\n  modifications: Partial<ExpoConfig>,\n  readOptions: GetConfigOptions = {},\n  writeOptions: WriteConfigOptions = {}\n): Promise<{\n  type: 'success' | 'warn' | 'fail';\n  message?: string;\n  config: AppJSONConfig | null;\n}> {\n  const config = getConfig(projectRoot, readOptions);\n  if (config.dynamicConfigPath) {\n    // We cannot automatically write to a dynamic config.\n    /* Currently we should just use the safest approach possible, informing the user that they'll need to manually modify their dynamic config.\n\n    if (config.staticConfigPath) {\n      // Both a dynamic and a static config exist.\n      if (config.dynamicConfigObjectType === 'function') {\n        // The dynamic config exports a function, this means it possibly extends the static config.\n      } else {\n        // Dynamic config ignores the static config, there isn't a reason to automatically write to it.\n        // Instead we should warn the user to add values to their dynamic config.\n      }\n    }\n    */\n    return {\n      type: 'warn',\n      message: `Cannot automatically write to dynamic config at: ${path.relative(\n        projectRoot,\n        config.dynamicConfigPath\n      )}`,\n      config: null,\n    };\n  } else if (config.staticConfigPath) {\n    // Static with no dynamic config, this means we can append to the config automatically.\n    let outputConfig: AppJSONConfig;\n    // If the config has an expo object (app.json) then append the options to that object.\n    if (config.rootConfig.expo) {\n      outputConfig = {\n        ...config.rootConfig,\n        expo: { ...config.rootConfig.expo, ...modifications },\n      };\n    } else {\n      // Otherwise (app.config.json) just add the config modification to the top most level.\n      outputConfig = { ...config.rootConfig, ...modifications };\n    }\n    if (!writeOptions.dryRun) {\n      await JsonFile.writeAsync(config.staticConfigPath, outputConfig, { json5: false });\n    }\n    return { type: 'success', config: outputConfig };\n  }\n\n  return { type: 'fail', message: 'No config exists', config: null };\n}\n\nfunction ensureConfigHasDefaultValues({\n  projectRoot,\n  exp,\n  pkg,\n  paths,\n  packageJsonPath,\n  skipSDKVersionRequirement = false,\n}: {\n  projectRoot: string;\n  exp: Partial<ExpoConfig> | null;\n  pkg: JSONObject;\n  skipSDKVersionRequirement?: boolean;\n  paths?: ConfigFilePaths;\n  packageJsonPath?: string;\n}): { exp: ExpoConfig; pkg: PackageJSONConfig } {\n  if (!exp) {\n    exp = {};\n  }\n  exp = withInternal(exp as any, {\n    projectRoot,\n    ...(paths ?? {}),\n    packageJsonPath,\n  });\n  // Defaults for package.json fields\n  const pkgName = typeof pkg.name === 'string' ? pkg.name : path.basename(projectRoot);\n  const pkgVersion = typeof pkg.version === 'string' ? pkg.version : '1.0.0';\n\n  const pkgWithDefaults = { ...pkg, name: pkgName, version: pkgVersion };\n\n  // Defaults for app.json/app.config.js fields\n  const name = exp.name ?? pkgName;\n  const slug = exp.slug ?? slugify(name.toLowerCase());\n  const version = exp.version ?? pkgVersion;\n  let description = exp.description;\n  if (!description && typeof pkg.description === 'string') {\n    description = pkg.description;\n  }\n\n  const expWithDefaults = { ...exp, name, slug, version, description };\n\n  let sdkVersion;\n  try {\n    sdkVersion = getExpoSDKVersion(projectRoot, expWithDefaults);\n  } catch (error) {\n    if (!skipSDKVersionRequirement) throw error;\n  }\n\n  let platforms = exp.platforms;\n  if (!platforms) {\n    platforms = getSupportedPlatforms(projectRoot);\n  }\n\n  return {\n    exp: { ...expWithDefaults, sdkVersion, platforms },\n    pkg: pkgWithDefaults,\n  };\n}\n\nconst DEFAULT_BUILD_PATH = `web-build`;\n\nexport function getWebOutputPath(config: { [key: string]: any } = {}): string {\n  if (process.env.WEBPACK_BUILD_OUTPUT_PATH) {\n    return process.env.WEBPACK_BUILD_OUTPUT_PATH;\n  }\n  const expo = config.expo || config || {};\n  return expo?.web?.build?.output || DEFAULT_BUILD_PATH;\n}\n\nexport function getNameFromConfig(exp: Record<string, any> = {}): {\n  appName?: string;\n  webName?: string;\n} {\n  // For RN CLI support\n  const appManifest = exp.expo || exp;\n  const { web = {} } = appManifest;\n\n  // rn-cli apps use a displayName value as well.\n  const appName = exp.displayName || appManifest.displayName || appManifest.name;\n  const webName = web.name || appName;\n\n  return {\n    appName,\n    webName,\n  };\n}\n\nexport function getDefaultTarget(\n  projectRoot: string,\n  exp?: Pick<ExpoConfig, 'sdkVersion'>\n): ProjectTarget {\n  exp ??= getConfig(projectRoot, { skipSDKVersionRequirement: true }).exp;\n\n  // before SDK 37, always default to managed to preserve previous behavior\n  if (exp.sdkVersion && exp.sdkVersion !== 'UNVERSIONED' && semver.lt(exp.sdkVersion, '37.0.0')) {\n    return 'managed';\n  }\n  return isBareWorkflowProject(projectRoot) ? 'bare' : 'managed';\n}\n\nfunction isBareWorkflowProject(projectRoot: string): boolean {\n  const [pkg] = getPackageJsonAndPath(projectRoot);\n\n  // TODO: Drop this\n  if (pkg.dependencies && pkg.dependencies.expokit) {\n    return false;\n  }\n\n  const xcodeprojFiles = globSync('ios/**/*.xcodeproj', {\n    absolute: true,\n    cwd: projectRoot,\n  });\n  if (xcodeprojFiles.length) {\n    return true;\n  }\n  const gradleFiles = globSync('android/**/*.gradle', {\n    absolute: true,\n    cwd: projectRoot,\n  });\n  if (gradleFiles.length) {\n    return true;\n  }\n\n  return false;\n}\n\n/**\n * Return a useful name describing the project config.\n * - dynamic: app.config.js\n * - static: app.json\n * - custom path app config relative to root folder\n * - both: app.config.js or app.json\n */\nexport function getProjectConfigDescription(projectRoot: string): string {\n  const paths = getConfigFilePaths(projectRoot);\n  return getProjectConfigDescriptionWithPaths(projectRoot, paths);\n}\n\n/**\n * Returns a string describing the configurations used for the given project root.\n * Will return null if no config is found.\n *\n * @param projectRoot\n * @param projectConfig\n */\nexport function getProjectConfigDescriptionWithPaths(\n  projectRoot: string,\n  projectConfig: ConfigFilePaths\n): string {\n  if (projectConfig.dynamicConfigPath) {\n    const relativeDynamicConfigPath = path.relative(projectRoot, projectConfig.dynamicConfigPath);\n    if (projectConfig.staticConfigPath) {\n      return `${relativeDynamicConfigPath} or ${path.relative(\n        projectRoot,\n        projectConfig.staticConfigPath\n      )}`;\n    }\n    return relativeDynamicConfigPath;\n  } else if (projectConfig.staticConfigPath) {\n    return path.relative(projectRoot, projectConfig.staticConfigPath);\n  }\n  // If a config doesn't exist, our tooling will generate a static app.json\n  return 'app.json';\n}\n\nexport * from './Config.types';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,SAAAA,UAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,SAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,IAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,GAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,MAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,KAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,MAAA;EAAA,MAAAL,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAG,KAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,aAAA;EAAA,MAAAN,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAI,YAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,QAAA;EAAA,MAAAP,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAK,OAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,SAAA;EAAA,MAAAR,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAM,QAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAaA,SAAAS,WAAA;EAAA,MAAAT,IAAA,GAAAE,OAAA;EAAAO,UAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAU,mBAAA;EAAA,MAAAV,IAAA,GAAAE,OAAA;EAAAQ,kBAAA,YAAAA,CAAA;IAAA,OAAAV,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAW,mBAAA;EAAA,MAAAX,IAAA,GAAAE,OAAA;EAAAS,kBAAA,YAAAA,CAAA;IAAA,OAAAX,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAY,cAAA;EAAA,MAAAZ,IAAA,GAAAE,OAAA;EAAAU,aAAA,YAAAA,CAAA;IAAA,OAAAZ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAa,oBAAA;EAAA,MAAAb,IAAA,GAAAE,OAAA;EAAAW,mBAAA,YAAAA,CAAA;IAAA,OAAAb,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAkdA,IAAAc,OAAA,GAAAZ,OAAA;AAAAa,MAAA,CAAAC,IAAA,CAAAF,OAAA,EAAAG,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAJ,OAAA,CAAAI,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAZ,OAAA,CAAAI,GAAA;IAAA;EAAA;AAAA;AAA+B,SAAAjB,uBAAA0B,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AA9c/B,IAAIG,wBAAwB,GAAG,KAAK;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,MAAY,EAAgB;EACpD,IAAI,CAACA,MAAM,EAAE,OAAOA,MAAM,KAAKC,SAAS,GAAG,IAAI,GAAGD,MAAM;EAExD,IAAIA,MAAM,CAACE,IAAI,IAAI,CAACJ,wBAAwB,EAAE;IAC5C,MAAMd,IAAI,GAAGD,MAAM,CAACC,IAAI,CAACgB,MAAM,CAAC,CAACG,MAAM,CAAEjB,GAAG,IAAKA,GAAG,KAAK,MAAM,CAAC;IAChE,IAAIF,IAAI,CAACoB,MAAM,EAAE;MACfN,wBAAwB,GAAG,IAAI;MAC/B,MAAMO,UAAU,GAAIC,GAAW,IAAK,aAAaA,GAAG,WAAW;MAC/D,MAAMC,QAAQ,GAAID,GAAW,IAAK,aAAaA,GAAG,WAAW;MAC7D,MAAME,QAAQ,GAAIF,GAAW,IAAK,YAAYA,GAAG,YAAY;MAC7D,MAAMG,MAAM,GAAGzB,IAAI,CAACoB,MAAM,GAAG,CAAC;MAC9BM,OAAO,CAACC,IAAI,CACVN,UAAU,CACRG,QAAQ,CAAC,WAAW,CAAC,GACnB,cAAcA,QAAQ,CAAC,QAAQ,CAAC,oCAAoCC,MAAM,GAAG,GAAG,GAAG,EAAE,oBAAoBzB,IAAI,CAC1G4B,GAAG,CAAE1B,GAAG,IAAK,IAAIA,GAAG,GAAG,CAAC,CACxB2B,IAAI,CAAC,IAAI,CAAC,IAAI,GACjBN,QAAQ,CAAC,+CAA+C,CAC5D,CACF,CAAC;IACH;EACF;EAEA,MAAM;IAAEO,IAAI;IAAE,GAAGZ;EAAK,CAAC,GAAGF,MAAM,CAACE,IAAI,IAAIF,MAAM;EAE/C,OAAO;IACLE,IAAI;IACJY;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,qBAAqBA,CAACC,WAAmB,EAAc;EAC9D,MAAMC,SAAqB,GAAG,EAAE;EAChC,IAAIC,sBAAW,CAACC,MAAM,CAACH,WAAW,EAAE,cAAc,CAAC,EAAE;IACnDC,SAAS,CAACG,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC;EAClC;EACA,IAAIF,sBAAW,CAACC,MAAM,CAACH,WAAW,EAAE,kBAAkB,CAAC,EAAE;IACvDC,SAAS,CAACG,IAAI,CAAC,KAAK,CAAC;EACvB;EACA,OAAOH,SAAS;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASI,SAASA,CAACL,WAAmB,EAAEM,OAAyB,GAAG,CAAC,CAAC,EAAiB;EAC5F,MAAMC,KAAK,GAAGC,kBAAkB,CAACR,WAAW,CAAC;EAE7C,MAAMS,eAAe,GAAGF,KAAK,CAACG,gBAAgB,GAAG,IAAAC,4BAAe,EAACJ,KAAK,CAACG,gBAAgB,CAAC,GAAG,IAAI;EAC/F;EACA,MAAME,UAAU,GAAIH,eAAe,IAAI,CAAC,CAAmB;EAC3D,MAAMI,YAAY,GAAG9B,gBAAgB,CAAC0B,eAAe,CAAC,IAAI,CAAC,CAAC;;EAE5D;EACA,MAAM,CAACK,WAAW,EAAEC,eAAe,CAAC,GAAGC,qBAAqB,CAAChB,WAAW,CAAC;EAEzE,SAASiB,mBAAmBA,CAC1BjC,MAAoB,EACpBkC,uBAAsC,EACtCC,yBAAkC,GAAG,KAAK,EAC1C;IACA,MAAMC,uBAAuB,GAAG;MAC9B,GAAGC,4BAA4B,CAAC;QAC9BrB,WAAW;QACXsB,GAAG,EAAEtC,MAAM,CAACE,IAAI;QAChBqC,GAAG,EAAET,WAAW;QAChBU,yBAAyB,EAAElB,OAAO,CAACkB,yBAAyB;QAC5DjB,KAAK;QACLQ;MACF,CAAC,CAAC;MACFjB,IAAI,EAAEd,MAAM,CAACc,IAAI;MACjBoB,uBAAuB;MACvBN,UAAU;MACVa,iBAAiB,EAAElB,KAAK,CAACkB,iBAAiB;MAC1Cf,gBAAgB,EAAEH,KAAK,CAACG,gBAAgB;MACxCgB,qBAAqB,EACnB,CAAC,CAACnB,KAAK,CAACG,gBAAgB,IAAI,CAAC,CAACH,KAAK,CAACkB,iBAAiB,IAAIN;IAC7D,CAAC;IAED,IAAIb,OAAO,CAACqB,cAAc,EAAE;MAC1B;MACAP,uBAAuB,CAACE,GAAG,CAACxB,IAAI,GAAGd,MAAM,CAACc,IAAI,IAAI,IAAI;IACxD;;IAEA;IACAsB,uBAAuB,CAACE,GAAG,GAAG,IAAAM,sCAAiB,EAC7CR,uBAAuB,CAACE,GAAG,EAC3B,CAAC,CAAChB,OAAO,CAACuB,WACZ,CAAC;IAED,IAAI,CAACvB,OAAO,CAACqB,cAAc,EAAE;MAC3B;MACA,OAAOP,uBAAuB,CAACE,GAAG,CAACxB,IAAI;IACzC;IAEA,IAAIQ,OAAO,CAACwB,cAAc,EAAE;MAC1B;;MAEA;MACA,OAAOV,uBAAuB,CAACE,GAAG,CAACS,SAAS;;MAE5C;MACA,IAAI,OAAO,IAAIX,uBAAuB,CAACE,GAAG,EAAE;QAC1C,OAAOF,uBAAuB,CAACE,GAAG,CAACU,KAAK;MAC1C;MACA,IAAIZ,uBAAuB,CAACE,GAAG,CAACW,GAAG,EAAEjD,MAAM,EAAE;QAC3C,OAAOoC,uBAAuB,CAACE,GAAG,CAACW,GAAG,CAACjD,MAAM;MAC/C;MACA,IAAIoC,uBAAuB,CAACE,GAAG,CAACY,OAAO,EAAElD,MAAM,EAAE;QAC/C,OAAOoC,uBAAuB,CAACE,GAAG,CAACY,OAAO,CAAClD,MAAM;MACnD;MAEA,OAAOoC,uBAAuB,CAACE,GAAG,CAACa,OAAO,EAAEC,sBAAsB;MAClE,OAAOhB,uBAAuB,CAACE,GAAG,CAACa,OAAO,EAAEE,mBAAmB;IACjE;IAEA,OAAOjB,uBAAuB;EAChC;;EAEA;EACA,SAASkB,gBAAgBA,CAACtD,MAAoB,EAAE;IAC9C,OAAOqC,4BAA4B,CAAC;MAClCrB,WAAW;MACXsB,GAAG,EAAEtC,MAAM,CAACE,IAAI;MAChBqC,GAAG,EAAET,WAAW;MAChBU,yBAAyB,EAAE,IAAI;MAC/BjB,KAAK;MACLQ;IACF,CAAC,CAAC,CAACO,GAAG;EACR;EAEA,IAAIf,KAAK,CAACkB,iBAAiB,EAAE;IAC3B;IACA,MAAM;MACJc,kBAAkB;MAClBvD,MAAM,EAAEwD,gBAAgB;MACxBrB;IACF,CAAC,GAAG,IAAAsB,6BAAgB,EAAClC,KAAK,CAACkB,iBAAiB,EAAE;MAC5CzB,WAAW;MACXU,gBAAgB,EAAEH,KAAK,CAACG,gBAAgB;MACxCK,eAAe;MACf/B,MAAM,EAAEsD,gBAAgB,CAACzB,YAAY;IACvC,CAAC,CAAC;IACF;IACA;IACA,MAAM6B,aAAa,GAAG3D,gBAAgB,CAACyD,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAC9D,OAAOvB,mBAAmB,CAACyB,aAAa,EAAEH,kBAAkB,EAAEpB,yBAAyB,CAAC;EAC1F;;EAEA;EACA,OAAOF,mBAAmB,CAACJ,YAAY,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC;AACtD;AAEO,SAAS8B,cAAcA,CAAC3C,WAAmB,EAAqB;EACrE,MAAM,CAACuB,GAAG,CAAC,GAAGP,qBAAqB,CAAChB,WAAW,CAAC;EAChD,OAAOuB,GAAG;AACZ;AAEA,SAASP,qBAAqBA,CAAChB,WAAmB,EAA+B;EAC/E,MAAMe,eAAe,GAAG,IAAA6B,4CAAsB,EAAC5C,WAAW,CAAC;EAC3D,OAAO,CAAC6C,mBAAQ,CAACC,IAAI,CAAC/B,eAAe,CAAC,EAAEA,eAAe,CAAC;AAC1D;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASP,kBAAkBA,CAACR,WAAmB,EAAmB;EACvE,OAAO;IACLyB,iBAAiB,EAAEsB,wBAAwB,CAAC/C,WAAW,CAAC;IACxDU,gBAAgB,EAAEsC,uBAAuB,CAAChD,WAAW;EACvD,CAAC;AACH;AAEA,SAAS+C,wBAAwBA,CAAC/C,WAAmB,EAAiB;EACpE,KAAK,MAAMiD,QAAQ,IAAI,CAAC,eAAe,EAAE,eAAe,CAAC,EAAE;IACzD,MAAMC,UAAU,GAAGC,eAAI,CAACtD,IAAI,CAACG,WAAW,EAAEiD,QAAQ,CAAC;IACnD,IAAIG,aAAE,CAACC,UAAU,CAACH,UAAU,CAAC,EAAE;MAC7B,OAAOA,UAAU;IACnB;EACF;EACA,OAAO,IAAI;AACb;AAEA,SAASF,uBAAuBA,CAAChD,WAAmB,EAAiB;EACnE,KAAK,MAAMiD,QAAQ,IAAI,CAAC,iBAAiB,EAAE,UAAU,CAAC,EAAE;IACtD,MAAMC,UAAU,GAAGC,eAAI,CAACtD,IAAI,CAACG,WAAW,EAAEiD,QAAQ,CAAC;IACnD,IAAIG,aAAE,CAACC,UAAU,CAACH,UAAU,CAAC,EAAE;MAC7B,OAAOA,UAAU;IACnB;EACF;EACA,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,eAAeI,iBAAiBA,CACrCtD,WAAmB,EACnBuD,aAAkC,EAClCC,WAA6B,GAAG,CAAC,CAAC,EAClCC,YAAgC,GAAG,CAAC,CAAC,EAKpC;EACD,MAAMzE,MAAM,GAAGqB,SAAS,CAACL,WAAW,EAAEwD,WAAW,CAAC;EAClD,IAAIxE,MAAM,CAACyC,iBAAiB,EAAE;IAC5B;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAEI,OAAO;MACLiC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,oDAAoDR,eAAI,CAACS,QAAQ,CACxE5D,WAAW,EACXhB,MAAM,CAACyC,iBACT,CAAC,EAAE;MACHzC,MAAM,EAAE;IACV,CAAC;EACH,CAAC,MAAM,IAAIA,MAAM,CAAC0B,gBAAgB,EAAE;IAClC;IACA,IAAImD,YAA2B;IAC/B;IACA,IAAI7E,MAAM,CAAC4B,UAAU,CAAC1B,IAAI,EAAE;MAC1B2E,YAAY,GAAG;QACb,GAAG7E,MAAM,CAAC4B,UAAU;QACpB1B,IAAI,EAAE;UAAE,GAAGF,MAAM,CAAC4B,UAAU,CAAC1B,IAAI;UAAE,GAAGqE;QAAc;MACtD,CAAC;IACH,CAAC,MAAM;MACL;MACAM,YAAY,GAAG;QAAE,GAAG7E,MAAM,CAAC4B,UAAU;QAAE,GAAG2C;MAAc,CAAC;IAC3D;IACA,IAAI,CAACE,YAAY,CAACK,MAAM,EAAE;MACxB,MAAMjB,mBAAQ,CAACkB,UAAU,CAAC/E,MAAM,CAAC0B,gBAAgB,EAAEmD,YAAY,EAAE;QAAEG,KAAK,EAAE;MAAM,CAAC,CAAC;IACpF;IACA,OAAO;MAAEN,IAAI,EAAE,SAAS;MAAE1E,MAAM,EAAE6E;IAAa,CAAC;EAClD;EAEA,OAAO;IAAEH,IAAI,EAAE,MAAM;IAAEC,OAAO,EAAE,kBAAkB;IAAE3E,MAAM,EAAE;EAAK,CAAC;AACpE;AAEA,SAASqC,4BAA4BA,CAAC;EACpCrB,WAAW;EACXsB,GAAG;EACHC,GAAG;EACHhB,KAAK;EACLQ,eAAe;EACfS,yBAAyB,GAAG;AAQ9B,CAAC,EAA+C;EAC9C,IAAI,CAACF,GAAG,EAAE;IACRA,GAAG,GAAG,CAAC,CAAC;EACV;EACAA,GAAG,GAAG,IAAA2C,4BAAY,EAAC3C,GAAG,EAAS;IAC7BtB,WAAW;IACX,IAAIO,KAAK,IAAI,CAAC,CAAC,CAAC;IAChBQ;EACF,CAAC,CAAC;EACF;EACA,MAAMmD,OAAO,GAAG,OAAO3C,GAAG,CAAC4C,IAAI,KAAK,QAAQ,GAAG5C,GAAG,CAAC4C,IAAI,GAAGhB,eAAI,CAACiB,QAAQ,CAACpE,WAAW,CAAC;EACpF,MAAMqE,UAAU,GAAG,OAAO9C,GAAG,CAAC+C,OAAO,KAAK,QAAQ,GAAG/C,GAAG,CAAC+C,OAAO,GAAG,OAAO;EAE1E,MAAMC,eAAe,GAAG;IAAE,GAAGhD,GAAG;IAAE4C,IAAI,EAAED,OAAO;IAAEI,OAAO,EAAED;EAAW,CAAC;;EAEtE;EACA,MAAMF,IAAI,GAAG7C,GAAG,CAAC6C,IAAI,IAAID,OAAO;EAChC,MAAMM,IAAI,GAAGlD,GAAG,CAACkD,IAAI,IAAI,IAAAC,kBAAO,EAACN,IAAI,CAACO,WAAW,CAAC,CAAC,CAAC;EACpD,MAAMJ,OAAO,GAAGhD,GAAG,CAACgD,OAAO,IAAID,UAAU;EACzC,IAAIM,WAAW,GAAGrD,GAAG,CAACqD,WAAW;EACjC,IAAI,CAACA,WAAW,IAAI,OAAOpD,GAAG,CAACoD,WAAW,KAAK,QAAQ,EAAE;IACvDA,WAAW,GAAGpD,GAAG,CAACoD,WAAW;EAC/B;EAEA,MAAMC,eAAe,GAAG;IAAE,GAAGtD,GAAG;IAAE6C,IAAI;IAAEK,IAAI;IAAEF,OAAO;IAAEK;EAAY,CAAC;EAEpE,IAAIE,UAAU;EACd,IAAI;IACFA,UAAU,GAAG,IAAAC,sCAAiB,EAAC9E,WAAW,EAAE4E,eAAe,CAAC;EAC9D,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAI,CAACvD,yBAAyB,EAAE,MAAMuD,KAAK;EAC7C;EAEA,IAAI9E,SAAS,GAAGqB,GAAG,CAACrB,SAAS;EAC7B,IAAI,CAACA,SAAS,EAAE;IACdA,SAAS,GAAGF,qBAAqB,CAACC,WAAW,CAAC;EAChD;EAEA,OAAO;IACLsB,GAAG,EAAE;MAAE,GAAGsD,eAAe;MAAEC,UAAU;MAAE5E;IAAU,CAAC;IAClDsB,GAAG,EAAEgD;EACP,CAAC;AACH;AAEA,MAAMS,kBAAkB,GAAG,WAAW;AAE/B,SAASC,gBAAgBA,CAACjG,MAA8B,GAAG,CAAC,CAAC,EAAU;EAC5E,IAAIkG,OAAO,CAACC,GAAG,CAACC,yBAAyB,EAAE;IACzC,OAAOF,OAAO,CAACC,GAAG,CAACC,yBAAyB;EAC9C;EACA,MAAMlG,IAAI,GAAGF,MAAM,CAACE,IAAI,IAAIF,MAAM,IAAI,CAAC,CAAC;EACxC,OAAOE,IAAI,EAAEmG,GAAG,EAAEC,KAAK,EAAEC,MAAM,IAAIP,kBAAkB;AACvD;AAEO,SAASQ,iBAAiBA,CAAClE,GAAwB,GAAG,CAAC,CAAC,EAG7D;EACA;EACA,MAAMmE,WAAW,GAAGnE,GAAG,CAACpC,IAAI,IAAIoC,GAAG;EACnC,MAAM;IAAE+D,GAAG,GAAG,CAAC;EAAE,CAAC,GAAGI,WAAW;;EAEhC;EACA,MAAMC,OAAO,GAAGpE,GAAG,CAACqE,WAAW,IAAIF,WAAW,CAACE,WAAW,IAAIF,WAAW,CAACtB,IAAI;EAC9E,MAAMyB,OAAO,GAAGP,GAAG,CAAClB,IAAI,IAAIuB,OAAO;EAEnC,OAAO;IACLA,OAAO;IACPE;EACF,CAAC;AACH;AAEO,SAASC,gBAAgBA,CAC9B7F,WAAmB,EACnBsB,GAAoC,EACrB;EACfA,GAAG,KAAKjB,SAAS,CAACL,WAAW,EAAE;IAAEwB,yBAAyB,EAAE;EAAK,CAAC,CAAC,CAACF,GAAG;;EAEvE;EACA,IAAIA,GAAG,CAACuD,UAAU,IAAIvD,GAAG,CAACuD,UAAU,KAAK,aAAa,IAAIiB,iBAAM,CAACC,EAAE,CAACzE,GAAG,CAACuD,UAAU,EAAE,QAAQ,CAAC,EAAE;IAC7F,OAAO,SAAS;EAClB;EACA,OAAOmB,qBAAqB,CAAChG,WAAW,CAAC,GAAG,MAAM,GAAG,SAAS;AAChE;AAEA,SAASgG,qBAAqBA,CAAChG,WAAmB,EAAW;EAC3D,MAAM,CAACuB,GAAG,CAAC,GAAGP,qBAAqB,CAAChB,WAAW,CAAC;;EAEhD;EACA,IAAIuB,GAAG,CAAC0E,YAAY,IAAI1E,GAAG,CAAC0E,YAAY,CAACC,OAAO,EAAE;IAChD,OAAO,KAAK;EACd;EAEA,MAAMC,cAAc,GAAG,IAAAC,YAAQ,EAAC,oBAAoB,EAAE;IACpDC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAEtG;EACP,CAAC,CAAC;EACF,IAAImG,cAAc,CAAC/G,MAAM,EAAE;IACzB,OAAO,IAAI;EACb;EACA,MAAMmH,WAAW,GAAG,IAAAH,YAAQ,EAAC,qBAAqB,EAAE;IAClDC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAEtG;EACP,CAAC,CAAC;EACF,IAAIuG,WAAW,CAACnH,MAAM,EAAE;IACtB,OAAO,IAAI;EACb;EAEA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASoH,2BAA2BA,CAACxG,WAAmB,EAAU;EACvE,MAAMO,KAAK,GAAGC,kBAAkB,CAACR,WAAW,CAAC;EAC7C,OAAOyG,oCAAoC,CAACzG,WAAW,EAAEO,KAAK,CAAC;AACjE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASkG,oCAAoCA,CAClDzG,WAAmB,EACnB0G,aAA8B,EACtB;EACR,IAAIA,aAAa,CAACjF,iBAAiB,EAAE;IACnC,MAAMkF,yBAAyB,GAAGxD,eAAI,CAACS,QAAQ,CAAC5D,WAAW,EAAE0G,aAAa,CAACjF,iBAAiB,CAAC;IAC7F,IAAIiF,aAAa,CAAChG,gBAAgB,EAAE;MAClC,OAAO,GAAGiG,yBAAyB,OAAOxD,eAAI,CAACS,QAAQ,CACrD5D,WAAW,EACX0G,aAAa,CAAChG,gBAChB,CAAC,EAAE;IACL;IACA,OAAOiG,yBAAyB;EAClC,CAAC,MAAM,IAAID,aAAa,CAAChG,gBAAgB,EAAE;IACzC,OAAOyC,eAAI,CAACS,QAAQ,CAAC5D,WAAW,EAAE0G,aAAa,CAAChG,gBAAgB,CAAC;EACnE;EACA;EACA,OAAO,UAAU;AACnB", "ignoreList": []}