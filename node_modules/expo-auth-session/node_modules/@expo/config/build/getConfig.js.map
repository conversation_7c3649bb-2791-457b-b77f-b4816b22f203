{"version": 3, "file": "getConfig.js", "names": ["_jsonFile", "data", "_interopRequireDefault", "require", "_fs", "_Errors", "_evalConfig", "obj", "__esModule", "default", "readConfigFile", "configFile", "context", "existsSync", "evalConfig", "error", "isConfigError", "message", "getDynamicConfig", "config<PERSON><PERSON>", "request", "config", "ConfigError", "getStaticConfig", "JsonFile", "read", "json5"], "sources": ["../src/getConfig.ts"], "sourcesContent": ["import JsonFile from '@expo/json-file';\nimport { existsSync } from 'fs';\n\nimport { AppJSONConfig, ConfigContext, ExpoConfig } from './Config.types';\nimport { ConfigError } from './Errors';\nimport { DynamicConfigResults, evalConfig } from './evalConfig';\n\n// We cannot use async config resolution right now because Next.js doesn't support async configs.\n// If they don't add support for async Webpack configs then we may need to pull support for Next.js.\nfunction readConfigFile(configFile: string, context: ConfigContext): null | DynamicConfigResults {\n  // If the file doesn't exist then we should skip it and continue searching.\n  if (!existsSync(configFile)) {\n    return null;\n  }\n  try {\n    return evalConfig(configFile, context);\n  } catch (error: any) {\n    // @ts-ignore\n    error.isConfigError = true;\n    error.message = `Error reading Expo config at ${configFile}:\\n\\n${error.message}`;\n    throw error;\n  }\n}\n\nexport function getDynamicConfig(configPath: string, request: ConfigContext): DynamicConfigResults {\n  const config = readConfigFile(configPath, request);\n  if (config) {\n    // The config must be serialized and evaluated ahead of time so the spawned process can send it over.\n    return config;\n  }\n  // TODO: It seems this is only thrown if the file cannot be found (which may never happen).\n  // If so we should throw a more helpful error.\n  throw new ConfigError(`Failed to read config at: ${configPath}`, 'INVALID_CONFIG');\n}\n\nexport function getStaticConfig(configPath: string): AppJSONConfig | ExpoConfig {\n  const config = JsonFile.read(configPath, { json5: true });\n  if (config) {\n    return config as any;\n  }\n  throw new ConfigError(`Failed to read config at: ${configPath}`, 'INVALID_CONFIG');\n}\n"], "mappings": ";;;;;;;AAAA,SAAAA,UAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,SAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,IAAA;EAAA,MAAAH,IAAA,GAAAE,OAAA;EAAAC,GAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAI,QAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,OAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,YAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,WAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAgE,SAAAC,uBAAAK,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEhE;AACA;AACA,SAASG,cAAcA,CAACC,UAAkB,EAAEC,OAAsB,EAA+B;EAC/F;EACA,IAAI,CAAC,IAAAC,gBAAU,EAACF,UAAU,CAAC,EAAE;IAC3B,OAAO,IAAI;EACb;EACA,IAAI;IACF,OAAO,IAAAG,wBAAU,EAACH,UAAU,EAAEC,OAAO,CAAC;EACxC,CAAC,CAAC,OAAOG,KAAU,EAAE;IACnB;IACAA,KAAK,CAACC,aAAa,GAAG,IAAI;IAC1BD,KAAK,CAACE,OAAO,GAAG,gCAAgCN,UAAU,QAAQI,KAAK,CAACE,OAAO,EAAE;IACjF,MAAMF,KAAK;EACb;AACF;AAEO,SAASG,gBAAgBA,CAACC,UAAkB,EAAEC,OAAsB,EAAwB;EACjG,MAAMC,MAAM,GAAGX,cAAc,CAACS,UAAU,EAAEC,OAAO,CAAC;EAClD,IAAIC,MAAM,EAAE;IACV;IACA,OAAOA,MAAM;EACf;EACA;EACA;EACA,MAAM,KAAIC,qBAAW,EAAC,6BAA6BH,UAAU,EAAE,EAAE,gBAAgB,CAAC;AACpF;AAEO,SAASI,eAAeA,CAACJ,UAAkB,EAA8B;EAC9E,MAAME,MAAM,GAAGG,mBAAQ,CAACC,IAAI,CAACN,UAAU,EAAE;IAAEO,KAAK,EAAE;EAAK,CAAC,CAAC;EACzD,IAAIL,MAAM,EAAE;IACV,OAAOA,MAAM;EACf;EACA,MAAM,KAAIC,qBAAW,EAAC,6BAA6BH,UAAU,EAAE,EAAE,gBAAgB,CAAC;AACpF", "ignoreList": []}