{"version": 3, "file": "Errors.js", "names": ["ConfigError", "Error", "name", "isConfigError", "constructor", "message", "code", "cause", "exports"], "sources": ["../src/Errors.ts"], "sourcesContent": ["import { ConfigErrorCode } from './Config.types';\n\n/**\n * Based on `JsonFileError` from `@expo/json-file`\n */\nexport class ConfigError extends Error {\n  readonly name = 'ConfigError';\n  readonly isConfigError = true;\n\n  constructor(\n    message: string,\n    public code: ConfigErrorCode,\n    public cause?: Error\n  ) {\n    super(cause ? `${message}\\n└─ Cause: ${cause.name}: ${cause.message}` : message);\n  }\n}\n"], "mappings": ";;;;;;AAEA;AACA;AACA;AACO,MAAMA,WAAW,SAASC,KAAK,CAAC;EAC5BC,IAAI,GAAG,aAAa;EACpBC,aAAa,GAAG,IAAI;EAE7BC,WAAWA,CACTC,OAAe,EACRC,IAAqB,EACrBC,KAAa,EACpB;IACA,KAAK,CAACA,KAAK,GAAG,GAAGF,OAAO,eAAeE,KAAK,CAACL,IAAI,KAAKK,KAAK,CAACF,OAAO,EAAE,GAAGA,OAAO,CAAC;IAAC,KAH1EC,IAAqB,GAArBA,IAAqB;IAAA,KACrBC,KAAa,GAAbA,KAAa;EAGtB;AACF;AAACC,OAAA,CAAAR,WAAA,GAAAA,WAAA", "ignoreList": []}