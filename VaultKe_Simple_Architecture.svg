<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="1200" height="800" fill="#0D1117"/>
  
  <!-- Title -->
  <text x="600" y="30" text-anchor="middle" fill="#F0F6FC" font-size="20" font-weight="bold">VaultKe Mobile App Architecture</text>
  <text x="600" y="50" text-anchor="middle" fill="#8B949E" font-size="12">Chama Management Platform with Google Drive Backup</text>

  <!-- App Entry -->
  <rect x="500" y="70" width="200" height="30" rx="5" fill="#6366F1" stroke="#4F46E5"/>
  <text x="600" y="90" text-anchor="middle" fill="white" font-size="12">App.js / EnhancedApp.js</text>

  <!-- Context Layer -->
  <rect x="50" y="120" width="1100" height="60" rx="5" fill="#21262D" stroke="#30363D"/>
  <text x="70" y="140" fill="#F0F6FC" font-size="14" font-weight="bold">Context Layer - Global State Management</text>
  
  <rect x="70" y="150" width="120" height="20" rx="3" fill="#10B981"/>
  <text x="130" y="163" text-anchor="middle" fill="white" font-size="10">AppContext</text>
  
  <rect x="200" y="150" width="120" height="20" rx="3" fill="#10B981"/>
  <text x="260" y="163" text-anchor="middle" fill="white" font-size="10">ChamaContext</text>
  
  <rect x="330" y="150" width="120" height="20" rx="3" fill="#6B7280"/>
  <text x="390" y="163" text-anchor="middle" fill="white" font-size="10">Theme System</text>
  
  <rect x="460" y="150" width="120" height="20" rx="3" fill="#6B7280"/>
  <text x="520" y="163" text-anchor="middle" fill="white" font-size="10">Auth State</text>
  
  <rect x="590" y="150" width="120" height="20" rx="3" fill="#6B7280"/>
  <text x="650" y="163" text-anchor="middle" fill="white" font-size="10">Data Sync</text>

  <!-- Navigation Layer -->
  <rect x="50" y="200" width="1100" height="80" rx="5" fill="#161B22" stroke="#30363D"/>
  <text x="70" y="220" fill="#F0F6FC" font-size="14" font-weight="bold">Navigation Layer</text>
  
  <rect x="70" y="230" width="100" height="20" rx="3" fill="#6366F1"/>
  <text x="120" y="243" text-anchor="middle" fill="white" font-size="10">RootNavigator</text>
  
  <rect x="180" y="230" width="100" height="20" rx="3" fill="#DC2626"/>
  <text x="230" y="243" text-anchor="middle" fill="white" font-size="10">AuthStack</text>
  
  <rect x="290" y="230" width="100" height="20" rx="3" fill="#10B981"/>
  <text x="340" y="243" text-anchor="middle" fill="white" font-size="10">UserStack</text>
  
  <rect x="400" y="230" width="100" height="20" rx="3" fill="#F59E0B"/>
  <text x="450" y="243" text-anchor="middle" fill="white" font-size="10">AdminStack</text>
  
  <rect x="510" y="230" width="100" height="20" rx="3" fill="#8B5CF6"/>
  <text x="560" y="243" text-anchor="middle" fill="white" font-size="10">ChamaStack</text>
  
  <rect x="180" y="255" width="80" height="15" rx="2" fill="#374151"/>
  <text x="220" y="265" text-anchor="middle" fill="white" font-size="8">AuthTabs</text>
  
  <rect x="270" y="255" width="80" height="15" rx="2" fill="#374151"/>
  <text x="310" y="265" text-anchor="middle" fill="white" font-size="8">UserTabs</text>
  
  <rect x="360" y="255" width="80" height="15" rx="2" fill="#374151"/>
  <text x="400" y="265" text-anchor="middle" fill="white" font-size="8">AdminTabs</text>
  
  <rect x="450" y="255" width="80" height="15" rx="2" fill="#374151"/>
  <text x="490" y="265" text-anchor="middle" fill="white" font-size="8">ChamaTabs</text>

  <!-- Screens Layer -->
  <rect x="50" y="300" width="1100" height="120" rx="5" fill="#0D1117" stroke="#30363D"/>
  <text x="70" y="320" fill="#F0F6FC" font-size="14" font-weight="bold">Screens Layer (50+ Screens)</text>
  
  <!-- Auth Screens -->
  <rect x="70" y="330" width="80" height="15" rx="2" fill="#DC2626"/>
  <text x="110" y="340" text-anchor="middle" fill="white" font-size="8">Login</text>
  <rect x="70" y="350" width="80" height="15" rx="2" fill="#DC2626"/>
  <text x="110" y="360" text-anchor="middle" fill="white" font-size="8">Register</text>
  <rect x="70" y="370" width="80" height="15" rx="2" fill="#DC2626"/>
  <text x="110" y="380" text-anchor="middle" fill="white" font-size="8">ForgotPassword</text>
  <rect x="70" y="390" width="80" height="15" rx="2" fill="#DC2626"/>
  <text x="110" y="400" text-anchor="middle" fill="white" font-size="8">ResetPassword</text>
  
  <!-- User Screens -->
  <rect x="170" y="330" width="80" height="15" rx="2" fill="#10B981"/>
  <text x="210" y="340" text-anchor="middle" fill="white" font-size="8">Dashboard</text>
  <rect x="170" y="350" width="80" height="15" rx="2" fill="#10B981"/>
  <text x="210" y="360" text-anchor="middle" fill="white" font-size="8">Profile</text>
  <rect x="170" y="370" width="80" height="15" rx="2" fill="#10B981"/>
  <text x="210" y="380" text-anchor="middle" fill="white" font-size="8">Settings</text>
  <rect x="170" y="390" width="80" height="15" rx="2" fill="#10B981"/>
  <text x="210" y="400" text-anchor="middle" fill="white" font-size="8">Notifications</text>
  
  <!-- Chama Screens -->
  <rect x="270" y="330" width="80" height="15" rx="2" fill="#8B5CF6"/>
  <text x="310" y="340" text-anchor="middle" fill="white" font-size="8">ChamaList</text>
  <rect x="270" y="350" width="80" height="15" rx="2" fill="#8B5CF6"/>
  <text x="310" y="360" text-anchor="middle" fill="white" font-size="8">ChamaDetails</text>
  <rect x="270" y="370" width="80" height="15" rx="2" fill="#8B5CF6"/>
  <text x="310" y="380" text-anchor="middle" fill="white" font-size="8">Members</text>
  <rect x="270" y="390" width="80" height="15" rx="2" fill="#8B5CF6"/>
  <text x="310" y="400" text-anchor="middle" fill="white" font-size="8">AccountMgmt</text>
  
  <!-- Wallet Screens -->
  <rect x="370" y="330" width="80" height="15" rx="2" fill="#059669"/>
  <text x="410" y="340" text-anchor="middle" fill="white" font-size="8">Wallet</text>
  <rect x="370" y="350" width="80" height="15" rx="2" fill="#059669"/>
  <text x="410" y="360" text-anchor="middle" fill="white" font-size="8">Deposit</text>
  <rect x="370" y="370" width="80" height="15" rx="2" fill="#059669"/>
  <text x="410" y="380" text-anchor="middle" fill="white" font-size="8">Withdraw</text>
  <rect x="370" y="390" width="80" height="15" rx="2" fill="#059669"/>
  <text x="410" y="400" text-anchor="middle" fill="white" font-size="8">Transactions</text>
  
  <!-- Marketplace Screens -->
  <rect x="470" y="330" width="80" height="15" rx="2" fill="#F59E0B"/>
  <text x="510" y="340" text-anchor="middle" fill="white" font-size="8">Marketplace</text>
  <rect x="470" y="350" width="80" height="15" rx="2" fill="#F59E0B"/>
  <text x="510" y="360" text-anchor="middle" fill="white" font-size="8">Products</text>
  <rect x="470" y="370" width="80" height="15" rx="2" fill="#F59E0B"/>
  <text x="510" y="380" text-anchor="middle" fill="white" font-size="8">Cart</text>
  <rect x="470" y="390" width="80" height="15" rx="2" fill="#F59E0B"/>
  <text x="510" y="400" text-anchor="middle" fill="white" font-size="8">Orders</text>
  
  <!-- Chat Screens -->
  <rect x="570" y="330" width="80" height="15" rx="2" fill="#6366F1"/>
  <text x="610" y="340" text-anchor="middle" fill="white" font-size="8">Chat</text>
  <rect x="570" y="350" width="80" height="15" rx="2" fill="#6366F1"/>
  <text x="610" y="360" text-anchor="middle" fill="white" font-size="8">ChatRoom</text>
  <rect x="570" y="370" width="80" height="15" rx="2" fill="#6366F1"/>
  <text x="610" y="380" text-anchor="middle" fill="white" font-size="8">GroupChat</text>
  <rect x="570" y="390" width="80" height="15" rx="2" fill="#6366F1"/>
  <text x="610" y="400" text-anchor="middle" fill="white" font-size="8">UserSearch</text>
  
  <!-- Learning Screens -->
  <rect x="670" y="330" width="80" height="15" rx="2" fill="#EC4899"/>
  <text x="710" y="340" text-anchor="middle" fill="white" font-size="8">LearningHub</text>
  <rect x="670" y="350" width="80" height="15" rx="2" fill="#EC4899"/>
  <text x="710" y="360" text-anchor="middle" fill="white" font-size="8">Courses</text>
  <rect x="670" y="370" width="80" height="15" rx="2" fill="#EC4899"/>
  <text x="710" y="380" text-anchor="middle" fill="white" font-size="8">Videos</text>
  <rect x="670" y="390" width="80" height="15" rx="2" fill="#EC4899"/>
  <text x="710" y="400" text-anchor="middle" fill="white" font-size="8">Quizzes</text>
  
  <!-- Admin Screens -->
  <rect x="770" y="330" width="80" height="15" rx="2" fill="#F59E0B"/>
  <text x="810" y="340" text-anchor="middle" fill="white" font-size="8">AdminPanel</text>
  <rect x="770" y="350" width="80" height="15" rx="2" fill="#F59E0B"/>
  <text x="810" y="360" text-anchor="middle" fill="white" font-size="8">UserMgmt</text>
  <rect x="770" y="370" width="80" height="15" rx="2" fill="#F59E0B"/>
  <text x="810" y="380" text-anchor="middle" fill="white" font-size="8">Analytics</text>
  <rect x="770" y="390" width="80" height="15" rx="2" fill="#F59E0B"/>
  <text x="810" y="400" text-anchor="middle" fill="white" font-size="8">Security</text>
  
  <text x="900" y="370" fill="#8B949E" font-size="10">+30 more screens...</text>

  <!-- Services Layer -->
  <rect x="50" y="440" width="1100" height="80" rx="5" fill="#21262D" stroke="#30363D"/>
  <text x="70" y="460" fill="#F0F6FC" font-size="14" font-weight="bold">Services Layer</text>
  
  <rect x="70" y="470" width="100" height="20" rx="3" fill="#6366F1"/>
  <text x="120" y="483" text-anchor="middle" fill="white" font-size="10">ApiService</text>
  
  <rect x="180" y="470" width="100" height="20" rx="3" fill="#10B981"/>
  <text x="230" y="483" text-anchor="middle" fill="white" font-size="10">DatabaseService</text>
  
  <rect x="290" y="470" width="100" height="20" rx="3" fill="#DC2626"/>
  <text x="340" y="483" text-anchor="middle" fill="white" font-size="10">GoogleDrive</text>
  
  <rect x="400" y="470" width="100" height="20" rx="3" fill="#8B5CF6"/>
  <text x="450" y="483" text-anchor="middle" fill="white" font-size="10">SyncService</text>
  
  <rect x="510" y="470" width="100" height="20" rx="3" fill="#F59E0B"/>
  <text x="560" y="483" text-anchor="middle" fill="white" font-size="10">WebSocket</text>
  
  <rect x="620" y="470" width="100" height="20" rx="3" fill="#EC4899"/>
  <text x="670" y="483" text-anchor="middle" fill="white" font-size="10">AIService</text>
  
  <rect x="730" y="470" width="100" height="20" rx="3" fill="#6B7280"/>
  <text x="780" y="483" text-anchor="middle" fill="white" font-size="10">Notifications</text>
  
  <rect x="70" y="495" width="90" height="15" rx="2" fill="#374151"/>
  <text x="115" y="505" text-anchor="middle" fill="white" font-size="8">LightningData</text>
  
  <rect x="170" y="495" width="90" height="15" rx="2" fill="#374151"/>
  <text x="215" y="505" text-anchor="middle" fill="white" font-size="8">SmartPrefetch</text>
  
  <rect x="270" y="495" width="90" height="15" rx="2" fill="#374151"/>
  <text x="315" y="505" text-anchor="middle" fill="white" font-size="8">DataPreload</text>
  
  <rect x="370" y="495" width="90" height="15" rx="2" fill="#374151"/>
  <text x="415" y="505" text-anchor="middle" fill="white" font-size="8">IntelligentAI</text>

  <!-- Backend Integration -->
  <rect x="50" y="540" width="1100" height="60" rx="5" fill="#161B22" stroke="#30363D"/>
  <text x="70" y="560" fill="#F0F6FC" font-size="14" font-weight="bold">Backend Integration (Go API)</text>
  
  <rect x="70" y="570" width="80" height="20" rx="3" fill="#00D9FF"/>
  <text x="110" y="583" text-anchor="middle" fill="white" font-size="9">Auth API</text>
  
  <rect x="160" y="570" width="80" height="20" rx="3" fill="#00D9FF"/>
  <text x="200" y="583" text-anchor="middle" fill="white" font-size="9">Chama API</text>
  
  <rect x="250" y="570" width="80" height="20" rx="3" fill="#00D9FF"/>
  <text x="290" y="583" text-anchor="middle" fill="white" font-size="9">Wallet API</text>
  
  <rect x="340" y="570" width="80" height="20" rx="3" fill="#00D9FF"/>
  <text x="380" y="583" text-anchor="middle" fill="white" font-size="9">Marketplace</text>
  
  <rect x="430" y="570" width="80" height="20" rx="3" fill="#DC2626"/>
  <text x="470" y="583" text-anchor="middle" fill="white" font-size="9">Google Drive</text>
  
  <rect x="520" y="570" width="80" height="20" rx="3" fill="#00D9FF"/>
  <text x="560" y="583" text-anchor="middle" fill="white" font-size="9">M-Pesa API</text>
  
  <rect x="610" y="570" width="80" height="20" rx="3" fill="#00D9FF"/>
  <text x="650" y="583" text-anchor="middle" fill="white" font-size="9">Chat API</text>
  
  <rect x="700" y="570" width="80" height="20" rx="3" fill="#00D9FF"/>
  <text x="740" y="583" text-anchor="middle" fill="white" font-size="9">Admin API</text>

  <!-- Google Drive Feature Highlight -->
  <rect x="950" y="120" width="200" height="100" rx="5" fill="#DC2626" stroke="#F87171" stroke-width="2"/>
  <text x="1050" y="140" text-anchor="middle" fill="white" font-size="12" font-weight="bold">NEW FEATURE</text>
  <text x="1050" y="155" text-anchor="middle" fill="white" font-size="11">Google Drive Auth Link</text>
  <text x="1050" y="175" text-anchor="middle" fill="#FEE2E2" font-size="9">Clickable OAuth Button</text>
  <text x="1050" y="190" text-anchor="middle" fill="#FEE2E2" font-size="9">Browser Integration</text>
  <text x="1050" y="205" text-anchor="middle" fill="#FEE2E2" font-size="9">Auto Status Update</text>

  <!-- Key Features -->
  <text x="70" y="640" fill="#F0F6FC" font-size="14" font-weight="bold">Key Features:</text>
  <text x="70" y="660" fill="#8B949E" font-size="11">Multi-Dashboard Architecture (User/Admin/Chama) - Real-time Chat and Notifications - Marketplace and E-commerce</text>
  <text x="70" y="680" fill="#8B949E" font-size="11">Financial Management with M-Pesa Integration - Learning Management System - AI Assistant - Google Drive Backup</text>
  <text x="70" y="700" fill="#8B949E" font-size="11">Offline-First with Sync - Dark/Light Theme - Multi-language Support - Account Management Page</text>

  <!-- Connection Lines -->
  <line x1="600" y1="100" x2="600" y2="120" stroke="#30363D" stroke-width="1"/>
  <line x1="600" y1="180" x2="600" y2="200" stroke="#30363D" stroke-width="1"/>
  <line x1="600" y1="280" x2="600" y2="300" stroke="#30363D" stroke-width="1"/>
  <line x1="600" y1="420" x2="600" y2="440" stroke="#30363D" stroke-width="1"/>
  <line x1="600" y1="520" x2="600" y2="540" stroke="#30363D" stroke-width="1"/>
</svg>
