<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 896 2062" style="max-width: 896px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46"><style>#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .error-icon{fill:#a44141;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .edge-thickness-normal{stroke-width:1px;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .marker.cross{stroke:lightgrey;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 p{margin:0;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .cluster-label text{fill:#F9FFFE;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .cluster-label span{color:#F9FFFE;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .cluster-label span p{background-color:transparent;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .label text,#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 span{fill:#ccc;color:#ccc;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .node rect,#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .node circle,#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .node ellipse,#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .node polygon,#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .rough-node .label text,#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .node .label text,#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .image-shape .label,#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .icon-shape .label{text-anchor:middle;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .rough-node .label,#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .node .label,#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .image-shape .label,#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .icon-shape .label{text-align:center;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .node.clickable{cursor:pointer;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .arrowheadPath{fill:lightgrey;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .cluster text{fill:#F9FFFE;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .cluster span{color:#F9FFFE;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 rect.text{fill:none;stroke-width:0;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .icon-shape,#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .icon-shape p,#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .icon-shape rect,#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M448,62L448,66.167C448,70.333,448,78.667,448,86.333C448,94,448,101,448,104.5L448,108"></path><path marker-end="url(#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_1" d="M448,190L448,194.167C448,198.333,448,206.667,448.07,214.417C448.141,222.167,448.281,229.334,448.351,232.917L448.422,236.501"></path><path marker-end="url(#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_2" d="M359.837,429.837L322.865,450.698C285.892,471.558,211.946,513.279,174.973,539.64C138,566,138,577,138,582.5L138,588"></path><path marker-end="url(#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_E_3" d="M513.591,453.409L528.492,470.341C543.394,487.273,573.197,521.136,588.098,543.568C603,566,603,577,603,582.5L603,588"></path><path marker-end="url(#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_F_4" d="M138,670L138,674.167C138,678.333,138,686.667,138,694.333C138,702,138,709,138,712.5L138,716"></path><path marker-end="url(#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_G_5" d="M138,798L138,802.167C138,806.333,138,814.667,138,822.333C138,830,138,837,138,840.5L138,844"></path><path marker-end="url(#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_H_6" d="M138,926L138,930.167C138,934.333,138,942.667,138,950.333C138,958,138,965,138,968.5L138,972"></path><path marker-end="url(#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_I_7" d="M138,1054L138,1058.167C138,1062.333,138,1070.667,138,1078.333C138,1086,138,1093,138,1096.5L138,1100"></path><path marker-end="url(#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_J_8" d="M138,1182L138,1186.167C138,1190.333,138,1198.667,138,1206.333C138,1214,138,1221,138,1224.5L138,1228"></path><path marker-end="url(#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_K_9" d="M138,1310L138,1314.167C138,1318.333,138,1326.667,138,1334.333C138,1342,138,1349,138,1352.5L138,1356"></path><path marker-end="url(#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_L_10" d="M138,1438L138,1442.167C138,1446.333,138,1454.667,138,1462.333C138,1470,138,1477,138,1480.5L138,1484"></path><path marker-end="url(#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_M_11" d="M138,1542L138,1546.167C138,1550.333,138,1558.667,138,1566.333C138,1574,138,1581,138,1584.5L138,1588"></path><path marker-end="url(#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M_N_12" d="M138,1670L138,1674.167C138,1678.333,138,1686.667,138,1694.333C138,1702,138,1709,138,1712.5L138,1716"></path><path marker-end="url(#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_N_O_13" d="M138,1798L138,1802.167C138,1806.333,138,1814.667,138,1822.333C138,1830,138,1837,138,1840.5L138,1844"></path><path marker-end="url(#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_O_P_14" d="M138,1926L138,1930.167C138,1934.333,138,1942.667,138,1950.333C138,1958,138,1965,138,1968.5L138,1972"></path><path marker-end="url(#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_Q_15" d="M508.547,670L498.456,674.167C488.365,678.333,468.182,686.667,458.091,694.333C448,702,448,709,448,712.5L448,716"></path><path marker-end="url(#mermaid-088bb085-27b3-40fe-81ed-5996ea45bb46_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_R_16" d="M697.453,670L707.544,674.167C717.635,678.333,737.818,686.667,747.909,694.333C758,702,758,709,758,712.5L758,716"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(138, 555)" class="edgeLabel"><g transform="translate(-10.2265625, -12)" class="label"><foreignObject height="24" width="20.453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g transform="translate(603, 555)" class="edgeLabel"><g transform="translate(-13.0546875, -12)" class="label"><foreignObject height="24" width="26.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(448, 35)" id="flowchart-A-74" class="node default"><rect height="54" width="204.078125" y="-27" x="-102.0390625" style="" class="basic label-container"></rect><g transform="translate(-72.0390625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="144.078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User opens Settings</p></span></div></foreignObject></g></g><g transform="translate(448, 151)" id="flowchart-B-75" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Scrolls to Google Drive Backup card</p></span></div></foreignObject></g></g><g transform="translate(448, 379)" id="flowchart-C-77" class="node default"><polygon transform="translate(-139,139)" class="label-container" points="139,0 278,-139 139,-278 0,-139"></polygon><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Is Google Drive Connected?</p></span></div></foreignObject></g></g><g transform="translate(138, 631)" id="flowchart-D-79" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Shows ❌ Disconnected status</p></span></div></foreignObject></g></g><g transform="translate(603, 631)" id="flowchart-E-81" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Shows ✅ Connected status</p></span></div></foreignObject></g></g><g transform="translate(138, 759)" id="flowchart-F-83" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User clicks 'Connect Google Drive'</p></span></div></foreignObject></g></g><g transform="translate(138, 887)" id="flowchart-G-85" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Alert: Authentication needed</p></span></div></foreignObject></g></g><g transform="translate(138, 1015)" id="flowchart-H-87" class="node default"><rect height="78" width="260" y="-39" x="-130" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🆕 Authentication Link Appears</p></span></div></foreignObject></g></g><g transform="translate(138, 1143)" id="flowchart-I-89" class="node default"><rect height="78" width="260" y="-39" x="-130" style="fill:#4caf50 !important" class="basic label-container"></rect><g transform="translate(-100, -24)" style="color:#fff !important" class="label"><rect></rect><foreignObject height="48" width="200"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(255, 255, 255) !important; display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;"><span class="nodeLabel" style="color:#fff !important"><p>User clicks 'Authenticate with Google' button</p></span></div></foreignObject></g></g><g transform="translate(138, 1271)" id="flowchart-J-91" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Browser opens with OAuth URL</p></span></div></foreignObject></g></g><g transform="translate(138, 1399)" id="flowchart-K-93" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User completes Google authentication</p></span></div></foreignObject></g></g><g transform="translate(138, 1515)" id="flowchart-L-95" class="node default"><rect height="54" width="196.953125" y="-27" x="-98.4765625" style="" class="basic label-container"></rect><g transform="translate(-68.4765625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="136.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User returns to app</p></span></div></foreignObject></g></g><g transform="translate(138, 1631)" id="flowchart-M-97" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User clicks 'Check Connection'</p></span></div></foreignObject></g></g><g transform="translate(138, 1759)" id="flowchart-N-99" class="node default"><rect height="78" width="260" y="-39" x="-130" style="fill:#4caf50 !important" class="basic label-container"></rect><g transform="translate(-100, -24)" style="color:#fff !important" class="label"><rect></rect><foreignObject height="48" width="200"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(255, 255, 255) !important; display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;"><span class="nodeLabel" style="color:#fff !important"><p>Status updates to ✅ Connected</p></span></div></foreignObject></g></g><g transform="translate(138, 1887)" id="flowchart-O-101" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Authentication link disappears</p></span></div></foreignObject></g></g><g transform="translate(138, 2015)" id="flowchart-P-103" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Backup/Restore options available</p></span></div></foreignObject></g></g><g transform="translate(448, 759)" id="flowchart-Q-105" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User can backup/restore data</p></span></div></foreignObject></g></g><g transform="translate(758, 759)" id="flowchart-R-107" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User can disconnect if needed</p></span></div></foreignObject></g></g></g></g></g></svg>