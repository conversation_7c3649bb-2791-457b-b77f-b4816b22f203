<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="primaryGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6366F1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4F46E5;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="secondaryGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="warningGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D97706;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
    </filter>
  </defs>

  <!-- Background -->
  <rect width="1400" height="1000" fill="#0D1117"/>
  
  <!-- Title -->
  <text x="700" y="30" text-anchor="middle" fill="#F0F6FC" font-size="24" font-weight="bold">VaultKe Mobile App Architecture</text>
  <text x="700" y="55" text-anchor="middle" fill="#8B949E" font-size="14">Comprehensive Chama Management Platform with Google Drive Backup</text>

  <!-- App Entry Point -->
  <rect x="600" y="80" width="200" height="40" rx="8" fill="url(#primaryGrad)" filter="url(#shadow)"/>
  <text x="700" y="105" text-anchor="middle" fill="white" font-size="14" font-weight="bold">App.js / EnhancedApp.js</text>

  <!-- Context Layer -->
  <rect x="50" y="150" width="1300" height="80" rx="12" fill="#21262D" stroke="#30363D" stroke-width="2"/>
  <text x="70" y="175" fill="#F0F6FC" font-size="16" font-weight="bold">🔄 Context Layer (Global State Management)</text>
  
  <!-- Context Components -->
  <rect x="80" y="190" width="150" height="30" rx="6" fill="url(#secondaryGrad)"/>
  <text x="155" y="210" text-anchor="middle" fill="white" font-size="12">AppContext</text>
  
  <rect x="250" y="190" width="150" height="30" rx="6" fill="url(#secondaryGrad)"/>
  <text x="325" y="210" text-anchor="middle" fill="white" font-size="12">ChamaContext</text>
  
  <rect x="420" y="190" width="200" height="30" rx="6" fill="#6B7280"/>
  <text x="520" y="210" text-anchor="middle" fill="white" font-size="12">Theme: Dark/Light</text>
  
  <rect x="640" y="190" width="200" height="30" rx="6" fill="#6B7280"/>
  <text x="740" y="210" text-anchor="middle" fill="white" font-size="12">Auth: User/Admin/Chama</text>
  
  <rect x="860" y="190" width="200" height="30" rx="6" fill="#6B7280"/>
  <text x="960" y="210" text-anchor="middle" fill="white" font-size="12">Data: Sync/Offline</text>
  
  <rect x="1080" y="190" width="200" height="30" rx="6" fill="#6B7280"/>
  <text x="1180" y="210" text-anchor="middle" fill="white" font-size="12">Language: EN/SW</text>

  <!-- Navigation Layer -->
  <rect x="50" y="260" width="1300" height="120" rx="12" fill="#161B22" stroke="#30363D" stroke-width="2"/>
  <text x="70" y="285" fill="#F0F6FC" font-size="16" font-weight="bold">🧭 Navigation Layer</text>
  
  <!-- Root Navigator -->
  <rect x="80" y="300" width="180" height="30" rx="6" fill="url(#primaryGrad)"/>
  <text x="170" y="320" text-anchor="middle" fill="white" font-size="12">RootNavigator</text>
  
  <!-- Dashboard Stacks -->
  <rect x="280" y="300" width="150" height="30" rx="6" fill="#DC2626"/>
  <text x="355" y="320" text-anchor="middle" fill="white" font-size="12">AuthStack</text>
  
  <rect x="450" y="300" width="150" height="30" rx="6" fill="url(#secondaryGrad)"/>
  <text x="525" y="320" text-anchor="middle" fill="white" font-size="12">UserDashboardStack</text>
  
  <rect x="620" y="300" width="150" height="30" rx="6" fill="url(#warningGrad)"/>
  <text x="695" y="320" text-anchor="middle" fill="white" font-size="12">AdminDashboardStack</text>
  
  <rect x="790" y="300" width="150" height="30" rx="6" fill="#8B5CF6"/>
  <text x="865" y="320" text-anchor="middle" fill="white" font-size="12">ChamaDashboardStack</text>
  
  <!-- Tab Navigators -->
  <rect x="280" y="340" width="120" height="25" rx="4" fill="#374151"/>
  <text x="340" y="357" text-anchor="middle" fill="white" font-size="10">AuthTabNavigator</text>
  
  <rect x="420" y="340" width="120" height="25" rx="4" fill="#374151"/>
  <text x="480" y="357" text-anchor="middle" fill="white" font-size="10">UserTabNavigator</text>
  
  <rect x="560" y="340" width="120" height="25" rx="4" fill="#374151"/>
  <text x="620" y="357" text-anchor="middle" fill="white" font-size="10">AdminTabNavigator</text>
  
  <rect x="700" y="340" width="120" height="25" rx="4" fill="#374151"/>
  <text x="760" y="357" text-anchor="middle" fill="white" font-size="10">ChamaTabNavigator</text>

  <!-- Screens Layer -->
  <rect x="50" y="410" width="1300" height="200" rx="12" fill="#0D1117" stroke="#30363D" stroke-width="2"/>
  <text x="70" y="435" fill="#F0F6FC" font-size="16" font-weight="bold">📱 Screens Layer (50+ Screens)</text>
  
  <!-- Auth Screens -->
  <g>
    <rect x="80" y="450" width="120" height="25" rx="4" fill="#DC2626"/>
    <text x="140" y="467" text-anchor="middle" fill="white" font-size="10">Login</text>
    <rect x="80" y="480" width="120" height="25" rx="4" fill="#DC2626"/>
    <text x="140" y="497" text-anchor="middle" fill="white" font-size="10">Register</text>
    <rect x="80" y="510" width="120" height="25" rx="4" fill="#DC2626"/>
    <text x="140" y="527" text-anchor="middle" fill="white" font-size="10">ForgotPassword</text>
    <rect x="80" y="540" width="120" height="25" rx="4" fill="#DC2626"/>
    <text x="140" y="557" text-anchor="middle" fill="white" font-size="10">ResetPassword</text>
  </g>
  
  <!-- User Screens -->
  <g>
    <rect x="220" y="450" width="120" height="25" rx="4" fill="url(#secondaryGrad)"/>
    <text x="280" y="467" text-anchor="middle" fill="white" font-size="10">UserDashboard</text>
    <rect x="220" y="480" width="120" height="25" rx="4" fill="url(#secondaryGrad)"/>
    <text x="280" y="497" text-anchor="middle" fill="white" font-size="10">Profile</text>
    <rect x="220" y="510" width="120" height="25" rx="4" fill="url(#secondaryGrad)"/>
    <text x="280" y="527" text-anchor="middle" fill="white" font-size="10">Settings ⭐</text>
    <rect x="220" y="540" width="120" height="25" rx="4" fill="url(#secondaryGrad)"/>
    <text x="280" y="557" text-anchor="middle" fill="white" font-size="10">Notifications</text>
  </g>
  
  <!-- Chama Screens -->
  <g>
    <rect x="360" y="450" width="120" height="25" rx="4" fill="#8B5CF6"/>
    <text x="420" y="467" text-anchor="middle" fill="white" font-size="10">ChamaList</text>
    <rect x="360" y="480" width="120" height="25" rx="4" fill="#8B5CF6"/>
    <text x="420" y="497" text-anchor="middle" fill="white" font-size="10">ChamaDetails</text>
    <rect x="360" y="510" width="120" height="25" rx="4" fill="#8B5CF6"/>
    <text x="420" y="527" text-anchor="middle" fill="white" font-size="10">ChamaMembers</text>
    <rect x="360" y="540" width="120" height="25" rx="4" fill="#8B5CF6"/>
    <text x="420" y="557" text-anchor="middle" fill="white" font-size="10">AccountMgmt ⭐</text>
  </g>
  
  <!-- Wallet Screens -->
  <g>
    <rect x="500" y="450" width="120" height="25" rx="4" fill="#059669"/>
    <text x="560" y="467" text-anchor="middle" fill="white" font-size="10">Wallet</text>
    <rect x="500" y="480" width="120" height="25" rx="4" fill="#059669"/>
    <text x="560" y="497" text-anchor="middle" fill="white" font-size="10">Deposit</text>
    <rect x="500" y="510" width="120" height="25" rx="4" fill="#059669"/>
    <text x="560" y="527" text-anchor="middle" fill="white" font-size="10">Withdraw</text>
    <rect x="500" y="540" width="120" height="25" rx="4" fill="#059669"/>
    <text x="560" y="557" text-anchor="middle" fill="white" font-size="10">Transactions</text>
  </g>
  
  <!-- Marketplace Screens -->
  <g>
    <rect x="640" y="450" width="120" height="25" rx="4" fill="#F59E0B"/>
    <text x="700" y="467" text-anchor="middle" fill="white" font-size="10">Marketplace</text>
    <rect x="640" y="480" width="120" height="25" rx="4" fill="#F59E0B"/>
    <text x="700" y="497" text-anchor="middle" fill="white" font-size="10">ProductDetails</text>
    <rect x="640" y="510" width="120" height="25" rx="4" fill="#F59E0B"/>
    <text x="700" y="527" text-anchor="middle" fill="white" font-size="10">Cart</text>
    <rect x="640" y="540" width="120" height="25" rx="4" fill="#F59E0B"/>
    <text x="700" y="557" text-anchor="middle" fill="white" font-size="10">Orders</text>
  </g>
  
  <!-- Chat Screens -->
  <g>
    <rect x="780" y="450" width="120" height="25" rx="4" fill="#6366F1"/>
    <text x="840" y="467" text-anchor="middle" fill="white" font-size="10">Chat</text>
    <rect x="780" y="480" width="120" height="25" rx="4" fill="#6366F1"/>
    <text x="840" y="497" text-anchor="middle" fill="white" font-size="10">ChatRoom</text>
    <rect x="780" y="510" width="120" height="25" rx="4" fill="#6366F1"/>
    <text x="840" y="527" text-anchor="middle" fill="white" font-size="10">CreateGroupChat</text>
    <rect x="780" y="540" width="120" height="25" rx="4" fill="#6366F1"/>
    <text x="840" y="557" text-anchor="middle" fill="white" font-size="10">UserSearch</text>
  </g>
  
  <!-- Learning Screens -->
  <g>
    <rect x="920" y="450" width="120" height="25" rx="4" fill="#EC4899"/>
    <text x="980" y="467" text-anchor="middle" fill="white" font-size="10">LearningHub</text>
    <rect x="920" y="480" width="120" height="25" rx="4" fill="#EC4899"/>
    <text x="980" y="497" text-anchor="middle" fill="white" font-size="10">CourseDetail</text>
    <rect x="920" y="510" width="120" height="25" rx="4" fill="#EC4899"/>
    <text x="980" y="527" text-anchor="middle" fill="white" font-size="10">VideoPlayer</text>
    <rect x="920" y="540" width="120" height="25" rx="4" fill="#EC4899"/>
    <text x="980" y="557" text-anchor="middle" fill="white" font-size="10">QuizTaking</text>
  </g>
  
  <!-- Admin Screens -->
  <g>
    <rect x="1060" y="450" width="120" height="25" rx="4" fill="url(#warningGrad)"/>
    <text x="1120" y="467" text-anchor="middle" fill="white" font-size="10">AdminDashboard</text>
    <rect x="1060" y="480" width="120" height="25" rx="4" fill="url(#warningGrad)"/>
    <text x="1120" y="497" text-anchor="middle" fill="white" font-size="10">UserManagement</text>
    <rect x="1060" y="510" width="120" height="25" rx="4" fill="url(#warningGrad)"/>
    <text x="1120" y="527" text-anchor="middle" fill="white" font-size="10">SystemAnalytics</text>
    <rect x="1060" y="540" width="120" height="25" rx="4" fill="url(#warningGrad)"/>
    <text x="1120" y="557" text-anchor="middle" fill="white" font-size="10">SecurityCenter</text>
  </g>
  
  <!-- More Screens Indicator -->
  <text x="1200" y="500" fill="#8B949E" font-size="12">+30 more screens...</text>

  <!-- Services Layer -->
  <rect x="50" y="640" width="1300" height="120" rx="12" fill="#21262D" stroke="#30363D" stroke-width="2"/>
  <text x="70" y="665" fill="#F0F6FC" font-size="16" font-weight="bold">⚙️ Services Layer</text>
  
  <!-- Core Services -->
  <rect x="80" y="680" width="150" height="30" rx="6" fill="url(#primaryGrad)"/>
  <text x="155" y="700" text-anchor="middle" fill="white" font-size="12">ApiService</text>
  
  <rect x="250" y="680" width="150" height="30" rx="6" fill="url(#secondaryGrad)"/>
  <text x="325" y="700" text-anchor="middle" fill="white" font-size="12">DatabaseService</text>
  
  <rect x="420" y="680" width="150" height="30" rx="6" fill="#DC2626"/>
  <text x="495" y="700" text-anchor="middle" fill="white" font-size="12">GoogleDriveService ⭐</text>
  
  <rect x="590" y="680" width="150" height="30" rx="6" fill="#8B5CF6"/>
  <text x="665" y="700" text-anchor="middle" fill="white" font-size="12">SyncService</text>
  
  <rect x="760" y="680" width="150" height="30" rx="6" fill="#F59E0B"/>
  <text x="835" y="700" text-anchor="middle" fill="white" font-size="12">WebSocketService</text>
  
  <rect x="930" y="680" width="150" height="30" rx="6" fill="#EC4899"/>
  <text x="1005" y="700" text-anchor="middle" fill="white" font-size="12">AIService</text>
  
  <rect x="1100" y="680" width="150" height="30" rx="6" fill="#6B7280"/>
  <text x="1175" y="700" text-anchor="middle" fill="white" font-size="12">NotificationService</text>
  
  <!-- Specialized Services -->
  <rect x="80" y="720" width="140" height="25" rx="4" fill="#374151"/>
  <text x="150" y="737" text-anchor="middle" fill="white" font-size="10">LightningDataService</text>
  
  <rect x="240" y="720" width="140" height="25" rx="4" fill="#374151"/>
  <text x="310" y="737" text-anchor="middle" fill="white" font-size="10">SmartPrefetchService</text>
  
  <rect x="400" y="720" width="140" height="25" rx="4" fill="#374151"/>
  <text x="470" y="737" text-anchor="middle" fill="white" font-size="10">DataPreloadService</text>
  
  <rect x="560" y="720" width="140" height="25" rx="4" fill="#374151"/>
  <text x="630" y="737" text-anchor="middle" fill="white" font-size="10">IntelligentAIService</text>

  <!-- Backend Integration -->
  <rect x="50" y="790" width="1300" height="80" rx="12" fill="#161B22" stroke="#30363D" stroke-width="2"/>
  <text x="70" y="815" fill="#F0F6FC" font-size="16" font-weight="bold">🔗 Backend Integration (Go API)</text>
  
  <!-- Backend Services -->
  <rect x="80" y="830" width="120" height="25" rx="4" fill="#00D9FF"/>
  <text x="140" y="847" text-anchor="middle" fill="white" font-size="10">Auth API</text>
  
  <rect x="220" y="830" width="120" height="25" rx="4" fill="#00D9FF"/>
  <text x="280" y="847" text-anchor="middle" fill="white" font-size="10">Chama API</text>
  
  <rect x="360" y="830" width="120" height="25" rx="4" fill="#00D9FF"/>
  <text x="420" y="847" text-anchor="middle" fill="white" font-size="10">Wallet API</text>
  
  <rect x="500" y="830" width="120" height="25" rx="4" fill="#00D9FF"/>
  <text x="560" y="847" text-anchor="middle" fill="white" font-size="10">Marketplace API</text>
  
  <rect x="640" y="830" width="120" height="25" rx="4" fill="#DC2626"/>
  <text x="700" y="847" text-anchor="middle" fill="white" font-size="10">Google Drive API ⭐</text>
  
  <rect x="780" y="830" width="120" height="25" rx="4" fill="#00D9FF"/>
  <text x="840" y="847" text-anchor="middle" fill="white" font-size="10">M-Pesa API</text>
  
  <rect x="920" y="830" width="120" height="25" rx="4" fill="#00D9FF"/>
  <text x="980" y="847" text-anchor="middle" fill="white" font-size="10">Chat API</text>
  
  <rect x="1060" y="830" width="120" height="25" rx="4" fill="#00D9FF"/>
  <text x="1120" y="847" text-anchor="middle" fill="white" font-size="10">Admin API</text>

  <!-- Google Drive Feature Highlight -->
  <rect x="1200" y="150" width="180" height="120" rx="8" fill="#DC2626" stroke="#F87171" stroke-width="2" filter="url(#shadow)"/>
  <text x="1290" y="175" text-anchor="middle" fill="white" font-size="14" font-weight="bold">🆕 NEW FEATURE</text>
  <text x="1290" y="195" text-anchor="middle" fill="white" font-size="12">Google Drive Auth Link</text>
  <text x="1290" y="215" text-anchor="middle" fill="#FEE2E2" font-size="10">✅ Clickable OAuth Button</text>
  <text x="1290" y="230" text-anchor="middle" fill="#FEE2E2" font-size="10">✅ Browser Integration</text>
  <text x="1290" y="245" text-anchor="middle" fill="#FEE2E2" font-size="10">✅ Auto Status Update</text>
  <text x="1290" y="260" text-anchor="middle" fill="#FEE2E2" font-size="10">✅ Secure Backup</text>

  <!-- Key Features -->
  <text x="70" y="920" fill="#F0F6FC" font-size="16" font-weight="bold">🌟 Key Features:</text>
  <text x="70" y="945" fill="#8B949E" font-size="12">• Multi-Dashboard Architecture (User/Admin/Chama) • Real-time Chat & Notifications • Marketplace & E-commerce</text>
  <text x="70" y="965" fill="#8B949E" font-size="12">• Financial Management & M-Pesa Integration • Learning Management System • AI Assistant • Google Drive Backup ⭐</text>
  <text x="70" y="985" fill="#8B949E" font-size="12">• Offline-First with Sync • Dark/Light Theme • Multi-language Support • Account Management Page</text>

  <!-- Connection Lines -->
  <line x1="700" y1="120" x2="700" y2="150" stroke="#30363D" stroke-width="2"/>
  <line x1="700" y1="230" x2="700" y2="260" stroke="#30363D" stroke-width="2"/>
  <line x1="700" y1="380" x2="700" y2="410" stroke="#30363D" stroke-width="2"/>
  <line x1="700" y1="610" x2="700" y2="640" stroke="#30363D" stroke-width="2"/>
  <line x1="700" y1="760" x2="700" y2="790" stroke="#30363D" stroke-width="2"/>
</svg>
