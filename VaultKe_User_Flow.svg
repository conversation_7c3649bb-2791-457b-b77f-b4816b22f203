<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <marker id="arrow" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto" markerUnits="strokeWidth">
      <path d="M0,0 L0,6 L9,3 z" fill="#8B949E"/>
    </marker>
    <marker id="arrowBlue" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto" markerUnits="strokeWidth">
      <path d="M0,0 L0,6 L9,3 z" fill="#6366F1"/>
    </marker>
    <marker id="arrowGreen" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto" markerUnits="strokeWidth">
      <path d="M0,0 L0,6 L9,3 z" fill="#10B981"/>
    </marker>
    <marker id="arrowRed" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto" markerUnits="strokeWidth">
      <path d="M0,0 L0,6 L9,3 z" fill="#DC2626"/>
    </marker>
  </defs>

  <!-- Background -->
  <rect width="1400" height="1000" fill="#0D1117"/>
  
  <!-- Title -->
  <text x="700" y="30" text-anchor="middle" fill="#F0F6FC" font-size="24" font-weight="bold">VaultKe User Flow Diagram</text>
  <text x="700" y="55" text-anchor="middle" fill="#8B949E" font-size="14">How Users Navigate Through Auth → User → Chama → Admin Dashboards</text>

  <!-- App Start -->
  <rect x="600" y="80" width="200" height="40" rx="8" fill="#6366F1" stroke="#4F46E5"/>
  <text x="700" y="105" text-anchor="middle" fill="white" font-size="14" font-weight="bold">App Launch</text>

  <!-- Auth Flow Section -->
  <rect x="50" y="150" width="1300" height="120" rx="8" fill="#21262D" stroke="#DC2626" stroke-width="2"/>
  <text x="70" y="175" fill="#DC2626" font-size="16" font-weight="bold">1. AUTHENTICATION FLOW</text>
  
  <!-- Auth Screens -->
  <rect x="100" y="190" width="120" height="30" rx="5" fill="#DC2626"/>
  <text x="160" y="210" text-anchor="middle" fill="white" font-size="12">Login Screen</text>
  
  <rect x="250" y="190" width="120" height="30" rx="5" fill="#DC2626"/>
  <text x="310" y="210" text-anchor="middle" fill="white" font-size="12">Register Screen</text>
  
  <rect x="400" y="190" width="120" height="30" rx="5" fill="#DC2626"/>
  <text x="460" y="210" text-anchor="middle" fill="white" font-size="12">Forgot Password</text>
  
  <rect x="550" y="190" width="120" height="30" rx="5" fill="#DC2626"/>
  <text x="610" y="210" text-anchor="middle" fill="white" font-size="12">Reset Password</text>
  
  <!-- Auth Success -->
  <rect x="750" y="190" width="150" height="30" rx="5" fill="#10B981"/>
  <text x="825" y="210" text-anchor="middle" fill="white" font-size="12">Authentication Success</text>
  
  <!-- Role Detection -->
  <rect x="950" y="190" width="120" height="30" rx="5" fill="#F59E0B"/>
  <text x="1010" y="210" text-anchor="middle" fill="white" font-size="12">Role Detection</text>
  
  <!-- Auth Flow Arrows -->
  <line x1="220" y1="205" x2="250" y2="205" stroke="#8B949E" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="370" y1="205" x2="400" y2="205" stroke="#8B949E" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="520" y1="205" x2="550" y2="205" stroke="#8B949E" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="670" y1="205" x2="750" y2="205" stroke="#10B981" stroke-width="2" marker-end="url(#arrowGreen)"/>
  <line x1="900" y1="205" x2="950" y2="205" stroke="#F59E0B" stroke-width="2" marker-end="url(#arrow)"/>

  <!-- Dashboard Selection -->
  <rect x="500" y="300" width="400" height="60" rx="8" fill="#161B22" stroke="#30363D" stroke-width="2"/>
  <text x="700" y="325" text-anchor="middle" fill="#F0F6FC" font-size="16" font-weight="bold">2. DASHBOARD SELECTION</text>
  <text x="700" y="345" text-anchor="middle" fill="#8B949E" font-size="12">Based on User Role & Context</text>

  <!-- User Dashboard Flow -->
  <rect x="50" y="400" width="400" height="200" rx="8" fill="#0D1117" stroke="#10B981" stroke-width="2"/>
  <text x="70" y="425" fill="#10B981" font-size="16" font-weight="bold">3A. USER DASHBOARD FLOW</text>
  
  <!-- User Main Tabs -->
  <rect x="70" y="445" width="80" height="25" rx="4" fill="#10B981"/>
  <text x="110" y="462" text-anchor="middle" fill="white" font-size="10">Dashboard</text>
  
  <rect x="160" y="445" width="80" height="25" rx="4" fill="#10B981"/>
  <text x="200" y="462" text-anchor="middle" fill="white" font-size="10">Wallet</text>
  
  <rect x="250" y="445" width="80" height="25" rx="4" fill="#10B981"/>
  <text x="290" y="462" text-anchor="middle" fill="white" font-size="10">Marketplace</text>
  
  <rect x="340" y="445" width="80" height="25" rx="4" fill="#10B981"/>
  <text x="380" y="462" text-anchor="middle" fill="white" font-size="10">Chat</text>
  
  <!-- User Sub-screens -->
  <rect x="70" y="480" width="70" height="20" rx="3" fill="#374151"/>
  <text x="105" y="493" text-anchor="middle" fill="white" font-size="8">Profile</text>
  
  <rect x="150" y="480" width="70" height="20" rx="3" fill="#374151"/>
  <text x="185" y="493" text-anchor="middle" fill="white" font-size="8">Settings</text>
  
  <rect x="230" y="480" width="70" height="20" rx="3" fill="#374151"/>
  <text x="265" y="493" text-anchor="middle" fill="white" font-size="8">Notifications</text>
  
  <rect x="310" y="480" width="70" height="20" rx="3" fill="#374151"/>
  <text x="345" y="493" text-anchor="middle" fill="white" font-size="8">Learning</text>
  
  <!-- Google Drive Feature -->
  <rect x="70" y="510" width="150" height="30" rx="5" fill="#DC2626"/>
  <text x="145" y="530" text-anchor="middle" fill="white" font-size="11">Google Drive Backup</text>
  
  <rect x="230" y="510" width="150" height="30" rx="5" fill="#6366F1"/>
  <text x="305" y="530" text-anchor="middle" fill="white" font-size="11">AI Assistant</text>
  
  <!-- User to Chama Transition -->
  <rect x="70" y="550" width="300" height="25" rx="4" fill="#8B5CF6"/>
  <text x="220" y="567" text-anchor="middle" fill="white" font-size="11">Join/Create Chama → Switch to Chama Dashboard</text>

  <!-- Chama Dashboard Flow -->
  <rect x="500" y="400" width="400" height="200" rx="8" fill="#0D1117" stroke="#8B5CF6" stroke-width="2"/>
  <text x="520" y="425" fill="#8B5CF6" font-size="16" font-weight="bold">3B. CHAMA DASHBOARD FLOW</text>
  
  <!-- Chama Main Tabs -->
  <rect x="520" y="445" width="80" height="25" rx="4" fill="#8B5CF6"/>
  <text x="560" y="462" text-anchor="middle" fill="white" font-size="10">Overview</text>
  
  <rect x="610" y="445" width="80" height="25" rx="4" fill="#8B5CF6"/>
  <text x="650" y="462" text-anchor="middle" fill="white" font-size="10">Members</text>
  
  <rect x="700" y="445" width="80" height="25" rx="4" fill="#8B5CF6"/>
  <text x="740" y="462" text-anchor="middle" fill="white" font-size="10">Finances</text>
  
  <rect x="790" y="445" width="80" height="25" rx="4" fill="#8B5CF6"/>
  <text x="830" y="462" text-anchor="middle" fill="white" font-size="10">Meetings</text>
  
  <!-- Chama Features -->
  <rect x="520" y="480" width="90" height="20" rx="3" fill="#374151"/>
  <text x="565" y="493" text-anchor="middle" fill="white" font-size="8">Contributions</text>
  
  <rect x="620" y="480" width="90" height="20" rx="3" fill="#374151"/>
  <text x="665" y="493" text-anchor="middle" fill="white" font-size="8">Loans</text>
  
  <rect x="720" y="480" width="90" height="20" rx="3" fill="#374151"/>
  <text x="765" y="493" text-anchor="middle" fill="white" font-size="8">Investments</text>
  
  <!-- Account Management -->
  <rect x="520" y="510" width="180" height="30" rx="5" fill="#DC2626"/>
  <text x="610" y="530" text-anchor="middle" fill="white" font-size="11">Account Management Page</text>
  
  <rect x="710" y="510" width="150" height="30" rx="5" fill="#F59E0B"/>
  <text x="785" y="530" text-anchor="middle" fill="white" font-size="11">Financial Reports</text>
  
  <!-- Chama to User/Admin -->
  <rect x="520" y="550" width="140" height="25" rx="4" fill="#10B981"/>
  <text x="590" y="567" text-anchor="middle" fill="white" font-size="10">Back to User Dashboard</text>
  
  <rect x="670" y="550" width="140" height="25" rx="4" fill="#F59E0B"/>
  <text x="740" y="567" text-anchor="middle" fill="white" font-size="10">Admin Access (if authorized)</text>

  <!-- Admin Dashboard Flow -->
  <rect x="950" y="400" width="400" height="200" rx="8" fill="#0D1117" stroke="#F59E0B" stroke-width="2"/>
  <text x="970" y="425" fill="#F59E0B" font-size="16" font-weight="bold">3C. ADMIN DASHBOARD FLOW</text>
  
  <!-- Admin Main Tabs -->
  <rect x="970" y="445" width="80" height="25" rx="4" fill="#F59E0B"/>
  <text x="1010" y="462" text-anchor="middle" fill="white" font-size="10">Overview</text>
  
  <rect x="1060" y="445" width="80" height="25" rx="4" fill="#F59E0B"/>
  <text x="1100" y="462" text-anchor="middle" fill="white" font-size="10">Users</text>
  
  <rect x="1150" y="445" width="80" height="25" rx="4" fill="#F59E0B"/>
  <text x="1190" y="462" text-anchor="middle" fill="white" font-size="10">Chamas</text>
  
  <rect x="1240" y="445" width="80" height="25" rx="4" fill="#F59E0B"/>
  <text x="1280" y="462" text-anchor="middle" fill="white" font-size="10">System</text>
  
  <!-- Admin Features -->
  <rect x="970" y="480" width="90" height="20" rx="3" fill="#374151"/>
  <text x="1015" y="493" text-anchor="middle" fill="white" font-size="8">User Management</text>
  
  <rect x="1070" y="480" width="90" height="20" rx="3" fill="#374151"/>
  <text x="1115" y="493" text-anchor="middle" fill="white" font-size="8">Analytics</text>
  
  <rect x="1170" y="480" width="90" height="20" rx="3" fill="#374151"/>
  <text x="1215" y="493" text-anchor="middle" fill="white" font-size="8">Security</text>
  
  <!-- Admin Tools -->
  <rect x="970" y="510" width="120" height="30" rx="5" fill="#DC2626"/>
  <text x="1030" y="530" text-anchor="middle" fill="white" font-size="11">System Health</text>
  
  <rect x="1100" y="510" width="120" height="30" rx="5" fill="#6366F1"/>
  <text x="1160" y="530" text-anchor="middle" fill="white" font-size="11">Audit Logs</text>
  
  <rect x="1230" y="510" width="120" height="30" rx="5" fill="#8B5CF6"/>
  <text x="1290" y="530" text-anchor="middle" fill="white" font-size="11">Content Moderation</text>
  
  <!-- Admin to User -->
  <rect x="970" y="550" width="200" height="25" rx="4" fill="#10B981"/>
  <text x="1070" y="567" text-anchor="middle" fill="white" font-size="10">Switch to User Dashboard</text>

  <!-- Cross-Dashboard Features -->
  <rect x="200" y="650" width="1000" height="80" rx="8" fill="#21262D" stroke="#30363D" stroke-width="2"/>
  <text x="220" y="675" fill="#F0F6FC" font-size="16" font-weight="bold">4. CROSS-DASHBOARD FEATURES</text>
  
  <!-- Shared Features -->
  <rect x="220" y="690" width="100" height="25" rx="4" fill="#6366F1"/>
  <text x="270" y="707" text-anchor="middle" fill="white" font-size="10">Notifications</text>
  
  <rect x="330" y="690" width="100" height="25" rx="4" fill="#EC4899"/>
  <text x="380" y="707" text-anchor="middle" fill="white" font-size="10">Chat System</text>
  
  <rect x="440" y="690" width="100" height="25" rx="4" fill="#10B981"/>
  <text x="490" y="707" text-anchor="middle" fill="white" font-size="10">Profile Settings</text>
  
  <rect x="550" y="690" width="100" height="25" rx="4" fill="#F59E0B"/>
  <text x="600" y="707" text-anchor="middle" fill="white" font-size="10">Theme Toggle</text>
  
  <rect x="660" y="690" width="100" height="25" rx="4" fill="#8B5CF6"/>
  <text x="710" y="707" text-anchor="middle" fill="white" font-size="10">Language</text>
  
  <rect x="770" y="690" width="100" height="25" rx="4" fill="#DC2626"/>
  <text x="820" y="707" text-anchor="middle" fill="white" font-size="10">Google Drive</text>
  
  <rect x="880" y="690" width="100" height="25" rx="4" fill="#059669"/>
  <text x="930" y="707" text-anchor="middle" fill="white" font-size="10">AI Assistant</text>
  
  <rect x="990" y="690" width="100" height="25" rx="4" fill="#6B7280"/>
  <text x="1040" y="707" text-anchor="middle" fill="white" font-size="10">Help & Support</text>

  <!-- Navigation Patterns -->
  <rect x="50" y="760" width="1300" height="100" rx="8" fill="#161B22" stroke="#30363D" stroke-width="2"/>
  <text x="70" y="785" fill="#F0F6FC" font-size="16" font-weight="bold">5. NAVIGATION PATTERNS</text>
  
  <!-- Navigation Types -->
  <rect x="70" y="800" width="150" height="25" rx="4" fill="#6366F1"/>
  <text x="145" y="817" text-anchor="middle" fill="white" font-size="11">Tab Navigation</text>
  
  <rect x="240" y="800" width="150" height="25" rx="4" fill="#10B981"/>
  <text x="315" y="817" text-anchor="middle" fill="white" font-size="11">Stack Navigation</text>
  
  <rect x="410" y="800" width="150" height="25" rx="4" fill="#F59E0B"/>
  <text x="485" y="817" text-anchor="middle" fill="white" font-size="11">Modal Navigation</text>
  
  <rect x="580" y="800" width="150" height="25" rx="4" fill="#8B5CF6"/>
  <text x="655" y="817" text-anchor="middle" fill="white" font-size="11">Drawer Navigation</text>
  
  <rect x="750" y="800" width="150" height="25" rx="4" fill="#EC4899"/>
  <text x="825" y="817" text-anchor="middle" fill="white" font-size="11">Deep Linking</text>
  
  <rect x="920" y="800" width="150" height="25" rx="4" fill="#DC2626"/>
  <text x="995" y="817" text-anchor="middle" fill="white" font-size="11">Context Switching</text>
  
  <!-- Navigation Features -->
  <text x="70" y="845" fill="#8B949E" font-size="12">• Persistent bottom tabs • Contextual headers • Breadcrumb navigation • Quick actions • Search functionality</text>

  <!-- User Journey Summary -->
  <rect x="200" y="890" width="1000" height="80" rx="8" fill="#0D1117" stroke="#6366F1" stroke-width="2"/>
  <text x="220" y="915" fill="#6366F1" font-size="16" font-weight="bold">USER JOURNEY SUMMARY</text>
  <text x="220" y="935" fill="#8B949E" font-size="12">1. User launches app → 2. Authenticates (Login/Register) → 3. Role-based dashboard selection</text>
  <text x="220" y="955" fill="#8B949E" font-size="12">4. Navigate within dashboard features → 5. Switch between dashboards as needed → 6. Access cross-platform features</text>

  <!-- Main Flow Arrows -->
  <line x1="700" y1="120" x2="700" y2="150" stroke="#6366F1" stroke-width="3" marker-end="url(#arrowBlue)"/>
  <line x1="1010" y1="220" x2="700" y2="300" stroke="#F59E0B" stroke-width="3" marker-end="url(#arrow)"/>
  
  <!-- Dashboard Branch Arrows -->
  <line x1="600" y1="360" x2="250" y2="400" stroke="#10B981" stroke-width="3" marker-end="url(#arrowGreen)"/>
  <line x1="700" y1="360" x2="700" y2="400" stroke="#8B5CF6" stroke-width="3" marker-end="url(#arrow)"/>
  <line x1="800" y1="360" x2="1150" y2="400" stroke="#F59E0B" stroke-width="3" marker-end="url(#arrow)"/>
  
  <!-- Cross-dashboard arrows -->
  <line x1="370" y1="575" x2="520" y2="575" stroke="#8B5CF6" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="810" y1="575" x2="970" y2="575" stroke="#F59E0B" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="590" y1="575" x2="450" y2="575" stroke="#10B981" stroke-width="2" marker-end="url(#arrowGreen)"/>
  <line x1="1070" y1="575" x2="900" y2="575" stroke="#10B981" stroke-width="2" marker-end="url(#arrowGreen)"/>
  
  <!-- Features connection -->
  <line x1="250" y1="600" x2="700" y2="650" stroke="#30363D" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="700" y1="600" x2="700" y2="650" stroke="#30363D" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="1150" y1="600" x2="700" y2="650" stroke="#30363D" stroke-width="2" marker-end="url(#arrow)"/>

  <!-- Step Numbers -->
  <circle cx="700" cy="65" r="15" fill="#6366F1"/>
  <text x="700" y="71" text-anchor="middle" fill="white" font-size="12" font-weight="bold">START</text>
  
  <circle cx="70" cy="160" r="12" fill="#DC2626"/>
  <text x="70" y="166" text-anchor="middle" fill="white" font-size="10" font-weight="bold">1</text>
  
  <circle cx="520" cy="310" r="12" fill="#F59E0B"/>
  <text x="520" y="316" text-anchor="middle" fill="white" font-size="10" font-weight="bold">2</text>
  
  <circle cx="70" cy="410" r="12" fill="#10B981"/>
  <text x="70" y="416" text-anchor="middle" fill="white" font-size="10" font-weight="bold">3A</text>
  
  <circle cx="520" cy="410" r="12" fill="#8B5CF6"/>
  <text x="520" y="416" text-anchor="middle" fill="white" font-size="10" font-weight="bold">3B</text>
  
  <circle cx="970" cy="410" r="12" fill="#F59E0B"/>
  <text x="970" y="416" text-anchor="middle" fill="white" font-size="10" font-weight="bold">3C</text>
  
  <circle cx="220" cy="660" r="12" fill="#6366F1"/>
  <text x="220" y="666" text-anchor="middle" fill="white" font-size="10" font-weight="bold">4</text>
  
  <circle cx="70" cy="770" r="12" fill="#EC4899"/>
  <text x="70" y="776" text-anchor="middle" fill="white" font-size="10" font-weight="bold">5</text>
</svg>
